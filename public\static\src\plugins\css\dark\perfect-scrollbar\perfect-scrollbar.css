/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*
 * Container style
 */
body.dark .ps {
  overflow: hidden !important;
  overflow-anchor: none;
  -ms-overflow-style: none;
  touch-action: auto;
  -ms-touch-action: auto;
}

/*
 * Scrollbar rail styles
 */
body.dark .ps__rail-x {
  display: none;
  opacity: 0;
  transition: background-color 0.2s linear, opacity 0.2s linear;
  -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
  height: 10px;
  bottom: 0px;
  position: absolute;
}

body.dark .ps__rail-y {
  display: none;
  opacity: 0;
  transition: background-color 0.2s linear, opacity 0.2s linear;
  -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
  width: 10px;
  right: 0;
  position: absolute;
}

body.dark .ps--active-x > .ps__rail-x, body.dark .ps--active-y > .ps__rail-y {
  display: block;
  background-color: transparent;
}
body.dark .ps:hover > .ps__rail-x, body.dark .ps:hover > .ps__rail-y {
  opacity: 0.6;
}
body.dark .ps--focus > .ps__rail-x, body.dark .ps--focus > .ps__rail-y {
  opacity: 0.6;
}
body.dark .ps--scrolling-x > .ps__rail-x, body.dark .ps--scrolling-y > .ps__rail-y {
  opacity: 0.6;
}
body.dark .ps .ps__rail-x:hover, body.dark .ps .ps__rail-y:hover, body.dark .ps .ps__rail-x:focus, body.dark .ps .ps__rail-y:focus, body.dark .ps .ps__rail-x.ps--clicking, body.dark .ps .ps__rail-y.ps--clicking {
  background-color: transparent;
  opacity: 0.9;
}

/*
 * Scrollbar thumb styles
 */
body.dark .ps__thumb-x {
  background-color: #888ea8;
  border-radius: 6px;
  transition: background-color 0.2s linear, height 0.2s ease-in-out;
  -webkit-transition: background-color 0.2s linear, height 0.2s ease-in-out;
  height: 4px;
  bottom: 2px;
  position: absolute;
}
body.dark .ps__thumb-y {
  background-color: #515365;
  border-radius: 6px;
  transition: background-color 0.2s linear, width 0.2s ease-in-out;
  -webkit-transition: background-color 0.2s linear, width 0.2s ease-in-out;
  width: 4px;
  right: 2px;
  position: absolute;
}
body.dark .ps__rail-x:hover > .ps__thumb-x, body.dark .ps__rail-x:focus > .ps__thumb-x, body.dark .ps__rail-x.ps--clicking .ps__thumb-x {
  background-color: #3b3f5c;
  height: 6px;
}
body.dark .ps__rail-y:hover > .ps__thumb-y, body.dark .ps__rail-y:focus > .ps__thumb-y, body.dark .ps__rail-y.ps--clicking .ps__thumb-y {
  background-color: #3b3f5c;
  width: 6px;
}

/* MS supports */
@supports (-ms-overflow-style: none) {
  body.dark .ps {
    overflow: auto !important;
  }
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  body.dark .ps {
    overflow: auto !important;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
