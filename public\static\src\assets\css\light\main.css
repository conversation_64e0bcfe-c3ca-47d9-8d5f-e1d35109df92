@charset "UTF-8";
/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
html {
  min-height: 100%;
}

body {
  color: #888ea8;
  height: 100%;
  font-size: 0.875rem;
  background: #f1f2f3;
  overflow-x: hidden;
  overflow-y: auto;
  letter-spacing: 0.0312rem;
  font-family: "Nunito", sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  color: #3b3f5c;
}

a {
  text-decoration: none;
  background-color: transparent;
}

:focus {
  outline: none;
}

p {
  margin-top: 0;
  margin-bottom: 0.625rem;
  color: #515365;
}

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border-top: 1px solid #f1f2f3;
}

strong {
  font-weight: 600;
}

code {
  color: #e7515a;
}

body.dark .dark-element {
  display: block;
}

.dark-element {
  display: none;
}

body.dark .light-element {
  display: none;
}

.light-element {
  display: block;
}

select.form-custom::-ms-expand {
  display: none;
}

.custom-file-input:focus ~ .custom-file-label {
  border: 1px solid #3b3f5c;
  box-shadow: none;
}
.custom-file-input:focus ~ .custom-file-label::after {
  border: none;
  border-left: 1px solid #3b3f5c;
}

.lead a.btn.btn-primary.btn-lg {
  margin-top: 15px;
  border-radius: 4px;
}

.jumbotron {
  background-color: #1b2e4b;
}

.mark, mark {
  background-color: #bfc9d4;
}

.modal-content {
  background: #0e1726;
}

.code-section-container {
  margin-top: 20px;
  text-align: left;
}

.toggle-code-snippet {
  border: none;
  background-color: transparent !important;
  padding: 0px !important;
  box-shadow: none !important;
  color: #888ea8 !important;
  margin-bottom: -24px;
  border-bottom: 1px dashed #bfc9d4;
  border-radius: 0 !important;
}
.toggle-code-snippet svg {
  color: #888ea8;
}
.toggle-code-snippet .toggle-code-icon {
  width: 16px;
  height: 16px;
  transition: 0.3s;
  transform: rotate(-90deg);
  vertical-align: text-top;
}

.code-section-container.show-code .toggle-code-snippet .toggle-code-icon {
  transform: rotate(0deg);
}

.code-section {
  padding: 0;
  height: 0;
}

.code-section-container.show-code .code-section {
  margin-top: 20px;
  height: auto;
}

.code-section pre {
  margin-bottom: 0;
  height: 0;
  padding: 0;
  border-radius: 6px;
}

.code-section-container.show-code .code-section pre {
  height: auto;
  padding: 22px;
}

.code-section code {
  color: #fff;
}

@media (min-width: 1400px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: 1440px;
  }
}
/* Media Object */
.media {
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
}

.media-body {
  -ms-flex: 1;
  flex: 1;
}

/*blockquote*/
blockquote.blockquote {
  color: #009688;
  padding: 20px 20px 20px 14px;
  font-size: 0.875rem;
  background-color: #fff;
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
  border: 1px solid #e0e6ed;
  border-left: 2px solid #4361ee;
  box-shadow: 0 0.1px 0px rgba(0, 0, 0, 0), 0 0.2px 0px rgba(0, 0, 0, 0), 0 0.4px 0px rgba(0, 0, 0, 0), 0 0.6px 0px rgba(0, 0, 0, 0), 0 0.9px 0px rgba(0, 0, 0, 0.01), 0 1.2px 0px rgba(0, 0, 0, 0.01), 0 1.8px 0px rgba(0, 0, 0, 0.01), 0 2.6px 0px rgba(0, 0, 0, 0.01), 0 3.9px 0px rgba(0, 0, 0, 0.01), 0 7px 0px rgba(0, 0, 0, 0.01);
}
blockquote.blockquote > p {
  margin-bottom: 0;
}
blockquote .small:before, blockquote footer:before, blockquote small:before {
  content: "— ";
}
blockquote .small, blockquote footer, blockquote small {
  display: block;
  font-size: 80%;
  line-height: 1.42857143;
  color: #888ea8;
}
blockquote.media-object.m-o-border-right {
  border-right: 4px solid #009688;
  border-left: none;
}
blockquote.media-object .media .usr-img img {
  width: 55px;
}

/* Icon List */
.list-icon {
  list-style: none;
  padding: 0;
  margin-bottom: 0;
}
.list-icon li:not(:last-child) {
  margin-bottom: 15px;
}
.list-icon svg {
  width: 18px;
  height: 18px;
  color: #2196f3;
  margin-right: 2px;
  vertical-align: sub;
}
.list-icon .list-text {
  font-size: 14px;
  font-weight: 600;
  color: #515365;
  letter-spacing: 1px;
}

a {
  color: #515365;
  outline: none;
}
a:hover {
  color: #888ea8;
  text-decoration: none;
}
a:focus {
  outline: none;
  text-decoration: none;
}

button:focus {
  outline: none;
}

textarea {
  outline: none;
}
textarea:focus {
  outline: none;
}

.btn-link:hover {
  text-decoration: none;
}

span.blue {
  color: #4361ee;
}
span.green {
  color: #00ab55;
}
span.red {
  color: #e7515a;
}

/*      CARD    */
.card {
  border: 1px solid #e0e6ed;
  border-radius: 10px;
  background: #fff;
  box-shadow: rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px;
}

.card-img, .card-img-top {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.card {
  /* Card Style 2 */
  /* Card Style 3 */
  /* Card Style 4 */
  /* Card Style 5 */
  /* Card Style 6 */
  /* Card Style 7 */
}
.card .card-header {
  color: #3b3f5c;
  border-bottom: 1px solid #e0e6ed;
  padding: 12px 20px;
}
.card .card-footer {
  border-top: 1px solid #e0e6ed;
  padding: 12px 20px;
  background-color: transparent;
}
.card .card-body {
  padding: 24px 20px;
}
.card .card-title {
  color: #0e1726;
  line-height: 1.5;
}
.card .card-text {
  color: #888ea8;
}
.card .media img.card-media-image {
  border-radius: 50%;
  width: 45px;
  height: 45px;
}
.card .media .media-body .media-heading {
  font-size: 14px;
  font-weight: 500;
}
.card.bg-primary .card-title {
  color: #fff;
}
.card.bg-primary .card-text {
  color: #e0e6ed;
}
.card.bg-primary p {
  color: #e0e6ed;
}
.card.bg-primary a {
  color: #bfc9d4;
}
.card.bg-info .card-title {
  color: #fff;
}
.card.bg-info .card-text {
  color: #e0e6ed;
}
.card.bg-info p {
  color: #e0e6ed;
}
.card.bg-info a {
  color: #bfc9d4;
}
.card.bg-success .card-title {
  color: #fff;
}
.card.bg-success .card-text {
  color: #e0e6ed;
}
.card.bg-success p {
  color: #e0e6ed;
}
.card.bg-success a {
  color: #bfc9d4;
}
.card.bg-warning .card-title {
  color: #fff;
}
.card.bg-warning .card-text {
  color: #e0e6ed;
}
.card.bg-warning p {
  color: #e0e6ed;
}
.card.bg-warning a {
  color: #bfc9d4;
}
.card.bg-danger .card-title {
  color: #fff;
}
.card.bg-danger .card-text {
  color: #e0e6ed;
}
.card.bg-danger p {
  color: #e0e6ed;
}
.card.bg-danger a {
  color: #bfc9d4;
}
.card.bg-secondary .card-title {
  color: #fff;
}
.card.bg-secondary .card-text {
  color: #e0e6ed;
}
.card.bg-secondary p {
  color: #e0e6ed;
}
.card.bg-secondary a {
  color: #bfc9d4;
}
.card.bg-dark .card-title {
  color: #fff;
}
.card.bg-dark .card-text {
  color: #e0e6ed;
}
.card.bg-dark p {
  color: #e0e6ed;
}
.card.bg-dark a {
  color: #bfc9d4;
}
.card.style-2 {
  padding: 15px 18px;
  border-radius: 15px;
}
.card.style-2 .card-img, .card.style-2 .card-img-top {
  border-radius: 15px;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
}
.card.style-3 {
  padding: 10px 10px;
  border-radius: 15px;
  flex-direction: row;
}
.card.style-3 .card-img, .card.style-3 .card-img-top {
  border-radius: 15px;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  width: 50%;
  margin-right: 25px;
}
.card.style-4 .media img.card-media-image {
  width: 55px;
  height: 55px;
}
.card.style-4 .media .media-body .media-heading {
  font-size: 16px;
}
.card.style-4 .media .media-body .media-text {
  font-size: 14px;
}
.card.style-4 .progress {
  background-color: #ebedf2;
}
.card.style-4 .attachments {
  cursor: pointer;
}
.card.style-4 .attachments:hover {
  color: #00ab55;
}
.card.style-4 .attachments svg {
  width: 18px;
  height: 18px;
  stroke-width: 1.6;
}
.card.style-5 {
  flex-direction: row;
}
.card.style-5 .card-top-content {
  padding: 24px 0 24px 20px;
}
.card.style-5 .card-content {
  -ms-flex: 1;
  flex: 1;
}
.card.style-6 .badge:not(.badge-dot) {
  position: absolute;
  right: 8px;
  top: 8px;
}
.card.style-7 .card-img-top {
  border-radius: 10px;
}
.card.style-7 .card-header {
  position: absolute;
  width: 100%;
  top: 0;
  border: none;
  background-color: rgba(0, 0, 0, 0.3803921569);
  backdrop-filter: saturate(180%) blur(10px);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.card.style-7 .card-footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  border: none;
  background-color: rgba(0, 0, 0, 0.3803921569);
  backdrop-filter: saturate(180%) blur(10px);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.card.style-7 .card-title {
  color: #fff;
}
.card.style-7 .card-text {
  color: #e0e6ed;
}

@media (max-width: 575px) {
  /* Card Style 3 */
  .card.style-3 {
    flex-direction: column;
  }
  .card.style-3 .card-img, .card.style-3 .card-img-top {
    width: 100%;
    height: auto;
    margin-bottom: 15px;
  }
}
/* clears the 'X' from Chrome */
input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
  display: none;
}

/* clears the 'X' from Internet Explorer */
input[type=search]::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}

input[type=search]::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}

/*      Form Group Label       */
.form-group label, label {
  font-size: 15px;
  color: #0e1726;
  letter-spacing: 1px;
  display: inline-block;
  margin-bottom: 0.5rem;
}

/*  Disable forms     */
.custom-control-input:disabled ~ .custom-control-label {
  color: #d3d3d3;
  cursor: no-drop;
}

.form-control:disabled:not(.flatpickr-input), .form-control[readonly]:not(.flatpickr-input) {
  background-color: #f1f2f3;
  cursor: no-drop;
  color: #d3d3d3;
}
.form-control:disabled:focus, .form-control[readonly]:focus {
  background-color: #f1f2f3;
}
.form-control:disabled::-webkit-input-placeholder, .form-control:disabled::-ms-input-placeholder, .form-control:disabled::-moz-placeholder, .form-control[readonly]::-webkit-input-placeholder, .form-control[readonly]::-ms-input-placeholder, .form-control[readonly]::-moz-placeholder {
  color: #888ea8;
  font-size: 15px;
}

.custom-control-input:disabled ~ .form-check-input, .custom-control-input[disabled] ~ .form-check-input {
  background-color: #3b3f5c;
  cursor: no-drop;
}

/*      Form Control       */
.form-control {
  height: auto;
  border: 1px solid #bfc9d4;
  color: #3b3f5c;
  font-size: 15px;
  padding: 8px 10px;
  letter-spacing: 1px;
  padding: 0.75rem 1.25rem;
  border-radius: 6px;
  background: #fff;
  height: auto;
  transition: none;
}

.form-text {
  color: #fff;
}

@supports (-webkit-overflow-scrolling: touch) {
  /* CSS specific to iOS devices */
  .form-control {
    color: #0e1726;
  }
}
.form-control[type=range] {
  padding: 0;
}
.form-control:focus {
  box-shadow: none;
  border-color: #4361ee;
  color: #3b3f5c;
  background-color: #fff;
}
.form-control::-webkit-input-placeholder, .form-control::-ms-input-placeholder, .form-control::-moz-placeholder {
  color: #888ea8;
  font-size: 15px;
}
.form-control:focus::-webkit-input-placeholder, .form-control:focus::-ms-input-placeholder, .form-control:focus::-moz-placeholder {
  color: #bfc9d4;
  font-size: 15px;
}
.form-control.form-control-lg {
  font-size: 19px;
  padding: 11px 20px;
}
.form-control.form-control-sm {
  padding: 7px 16px;
  font-size: 13px;
}

.form-select.form-control-sm {
  padding: 7px 16px;
  font-size: 13px;
}

/*      Custom Select       */
.form-check {
  min-height: auto;
}

.form-check-input {
  background-color: #e0e6ed;
  border-color: #e0e6ed;
  width: 17px;
  height: 17px;
  margin-top: 0.21em;
  transition: background-color 0.15s ease-in-out, background-position 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.form-check-input:focus {
  border-color: #e0e6ed;
  box-shadow: none;
}
.form-check-input:checked {
  background-color: #4361ee;
  border-color: #4361ee;
}

.form-check:not(.form-switch) .form-check-input:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13 11' width='13' height='11' fill='none'%3e%3cpath d='M11.0426 1.02893C11.3258 0.695792 11.8254 0.655283 12.1585 0.938451C12.4917 1.22162 12.5322 1.72124 12.249 2.05437L5.51985 9.97104C5.23224 10.3094 4.72261 10.3451 4.3907 10.05L0.828197 6.88335C0.50141 6.59288 0.471975 6.09249 0.762452 5.7657C1.05293 5.43891 1.55332 5.40948 1.88011 5.69995L4.83765 8.32889L11.0426 1.02893Z' fill='%23FFFFFF'/%3e%3c/svg%3e");
  background-size: 60% 60%;
}
.form-check .form-check-input {
  margin-left: -1.6em;
}

.form-check-input:checked[type=checkbox]:not([role=switch]) {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13 11' width='13' height='11' fill='none'%3e%3cpath d='M11.0426 1.02893C11.3258 0.695792 11.8254 0.655283 12.1585 0.938451C12.4917 1.22162 12.5322 1.72124 12.249 2.05437L5.51985 9.97104C5.23224 10.3094 4.72261 10.3451 4.3907 10.05L0.828197 6.88335C0.50141 6.59288 0.471975 6.09249 0.762452 5.7657C1.05293 5.43891 1.55332 5.40948 1.88011 5.69995L4.83765 8.32889L11.0426 1.02893Z' fill='%23FFFFFF'/%3e%3c/svg%3e");
  background-size: 60% 60%;
}

/*      Custom Select       */
.form-select {
  height: auto;
  font-size: 15px;
  padding: 0.75rem 1.25rem;
  letter-spacing: 1px;
  border: 1px solid #bfc9d4;
  color: #3b3f5c;
  background-color: #fff;
  border-radius: 6px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%233b3f5c' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  transition: none;
}
.form-select.form-select-lg {
  font-size: 19px;
  padding: 11px 20px;
}
.form-select.form-select-sm {
  padding: 7px 16px;
  font-size: 13px;
}
.form-select:focus {
  box-shadow: none;
  border-color: #4361ee;
  color: #3b3f5c;
  background-color: #fff;
}

/*      Form Control File       */
.form-control-file {
  width: 100%;
  color: #805dca;
}
.form-control-file::-webkit-file-upload-button {
  letter-spacing: 1px;
  padding: 9px 20px;
  text-shadow: none;
  font-size: 12px;
  color: #fff;
  font-weight: normal;
  white-space: normal;
  word-wrap: break-word;
  transition: 0.2s ease-out;
  touch-action: manipulation;
  cursor: pointer;
  background-color: #805dca;
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  will-change: opacity, transform;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  border-radius: 4px;
  border: transparent;
  outline: none;
}
.form-control-file::-ms-file-upload-button {
  letter-spacing: 1px;
  padding: 9px 20px;
  text-shadow: none;
  font-size: 14px;
  color: #fff;
  font-weight: normal;
  white-space: normal;
  word-wrap: break-word;
  transition: 0.2s ease-out;
  touch-action: manipulation;
  cursor: pointer;
  background-color: #805dca;
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  will-change: opacity, transform;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  border-radius: 4px;
  border: transparent;
  outline: none;
}
.form-control-file.form-control-file-rounded::-webkit-file-upload-button {
  -webkit-border-radius: 1.875rem !important;
  -moz-border-radius: 1.875rem !important;
  -ms-border-radius: 1.875rem !important;
  -o-border-radius: 1.875rem !important;
  border-radius: 1.875rem !important;
}

select.form-control.form-custom {
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  vertical-align: middle;
  background: #fff url(../img/arrow-down.png) no-repeat right 0.75rem center;
  background-size: 13px 14px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/*      Form Control Custom File       */
.file-upload-input {
  padding: 0.375rem 0.75rem;
}
.file-upload-input::-webkit-file-upload-button {
  letter-spacing: 1px;
  padding: 9px 20px;
  text-shadow: none;
  font-size: 12px;
  color: #1b2e4b;
  font-weight: normal;
  white-space: normal;
  word-wrap: break-word;
  transition: 0.2s ease-out;
  touch-action: manipulation;
  cursor: pointer;
  background-color: #e0e6ed;
  will-change: opacity, transform;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  border: transparent;
  outline: none;
}
.file-upload-input::-webkit-file-upload-button:hover {
  background-color: #e0e6ed;
}
.file-upload-input.form-control-file-rounded::-webkit-file-upload-button {
  -webkit-border-radius: 1.875rem !important;
  -moz-border-radius: 1.875rem !important;
  -ms-border-radius: 1.875rem !important;
  -o-border-radius: 1.875rem !important;
  border-radius: 1.875rem !important;
}

.form-control[type=file]::file-selector-button, .form-control[type=file]::-webkit-file-upload-button {
  background-color: #e0e6ed !important;
  color: #1b2e4b;
}

/*      Input Group      */
.input-group button:hover, .input-group .btn:hover, .input-group button:focus, .input-group .btn:focus {
  transform: none;
}
.input-group .dropdown-menu {
  border: none;
  z-index: 1028;
  box-shadow: none;
  padding: 10px;
  padding: 0.35rem 0;
  right: auto;
  border-radius: 8px;
  box-shadow: none;
  background-color: #fff;
  border: 1px solid #e0e6ed;
}
.input-group .dropdown-menu a.dropdown-item {
  border-radius: 5px;
  width: 100%;
  padding: 6px 17px;
  clear: both;
  font-weight: 500;
  color: #0e1726;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  font-size: 13px;
}
.input-group .dropdown-menu a.dropdown-item:hover {
  color: #2196f3;
}
.input-group .dropdown-menu .dropdown-item:hover {
  color: #2196f3;
}
.input-group .dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e0e6ed;
}
.input-group .input-group-text {
  border: 1px solid #bfc9d4;
  background-color: #f1f2f3;
  color: #515365;
}
.input-group .input-group-text svg {
  color: #515365;
}
.input-group:hover .input-group-text svg {
  color: #4361ee;
  fill: rgba(27, 85, 226, 0.2392156863);
}
.input-group .input-group-append .input-group-text {
  border: 1px solid #bfc9d4;
  background-color: #f1f2f3;
  color: #515365;
}
.input-group .input-group-append .input-group-text svg {
  color: #888ea8;
}
.input-group:hover .input-group-append .input-group-text svg {
  color: #4361ee;
  fill: rgba(27, 85, 226, 0.2392156863);
}

/*      Input Group append       */
/*      Input Group Append       */
/*      Validation Customization      */
.invalid-feedback {
  color: #e7515a;
  font-size: 13px;
  letter-spacing: 1px;
}

.valid-feedback {
  color: #009688;
  font-size: 13px;
  letter-spacing: 1px;
}

.valid-tooltip {
  background-color: #009688;
}

.invalid-tooltip {
  background-color: #e7515a;
}

.custom-select.is-valid, .form-control.is-valid {
  border-color: #009688;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23009688' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-check'%3e%3cpolyline points='20 6 9 17 4 12'%3e%3c/polyline%3e%3c/svg%3e");
}

.was-validated .custom-select:valid, .was-validated .form-control:valid {
  border-color: #009688;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23009688' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-check'%3e%3cpolyline points='20 6 9 17 4 12'%3e%3c/polyline%3e%3c/svg%3e");
}

.custom-control-input.is-valid ~ .custom-control-label, .was-validated .custom-control-input:valid ~ .custom-control-label {
  color: #009688;
}

.form-control.is-invalid, .was-validated .form-control:invalid {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23e7515a' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-x'%3e%3cline x1='18' y1='6' x2='6' y2='18'%3e%3c/line%3e%3cline x1='6' y1='6' x2='18' y2='18'%3e%3c/line%3e%3c/svg%3e");
}

.custom-control-input.is-invalid ~ .custom-control-label, .was-validated .custom-control-input:invalid ~ .custom-control-label {
  color: #e7515a;
}

.dropdown-toggle:after, .dropup .dropdown-toggle::after, .dropend .dropdown-toggle::after, .dropstart .dropdown-toggle::before {
  display: none;
}

.dropdown-toggle svg.feather[class*=feather-chevron-] {
  width: 15px;
  height: 15px;
  vertical-align: middle;
}

.btn {
  padding: 0.4375rem 1.25rem;
  text-shadow: none;
  font-size: 14px;
  color: #3b3f5c;
  font-weight: normal;
  white-space: normal;
  word-wrap: break-word;
  transition: 0.2s ease-out;
  touch-action: manipulation;
  border-radius: 6px;
  cursor: pointer;
  background-color: #e0e6ed;
  will-change: opacity, transform;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
}
.btn svg {
  pointer-events: none;
  height: 22px;
  width: 22px;
  vertical-align: middle;
}
.btn .btn-text-inner {
  margin-left: 3px;
  vertical-align: middle;
  pointer-events: none;
}
.btn.btn-icon {
  padding: 7.5px 9px;
}
.btn.btn-icon.btn-rounded {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
}
.btn.rounded-circle {
  height: 40px;
  width: 40px;
  padding: 8px 8px;
}
.btn:hover {
  color: #3b3f5c;
  background-color: #f1f2f3;
  border-color: #d3d3d3;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-transform: translateY(-3px);
  transform: translateY(-3px);
}

.btn-group .btn:hover, .btn-group .btn:focus {
  -webkit-transform: none;
  transform: none;
}

.btn.disabled, .btn.btn[disabled] {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn.disabled:hover, .btn.btn[disabled]:hover {
  cursor: not-allowed;
}
.btn .caret {
  border-top-color: #0e1726;
  margin-top: 0;
  margin-left: 3px;
  vertical-align: middle;
}
.btn + .caret, .btn + .dropdown-toggle .caret {
  margin-left: 0;
}

.btn-group > .btn, .btn-group .btn {
  padding: 8px 14px;
}

.btn-group-lg > .btn, .btn-group-lg .btn {
  font-size: 1.125rem;
}
.btn-group-lg > .btn {
  padding: 0.625rem 1.5rem;
  font-size: 16px;
}

.btn-lg {
  padding: 0.625rem 1.5rem;
  font-size: 16px;
}

.btn-group > .btn.btn-lg, .btn-group .btn.btn-lg {
  padding: 0.625rem 1.5rem;
  font-size: 16px;
}

.btn-group-lg > .btn, .btn-group-lg .btn {
  font-size: 1.125rem;
}

.btn-group-sm > .btn, .btn-sm {
  font-size: 0.6875rem;
}

.btn-group > .btn.btn-sm, .btn-group .btn.btn-sm {
  font-size: 0.6875rem;
}
.btn-group .dropdown-menu {
  border: none;
  z-index: 1028;
  box-shadow: none;
  padding: 10px;
  padding: 0.35rem 0;
  right: auto;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e0e6ed;
}
.btn-group .dropdown-menu a.dropdown-item {
  border-radius: 5px;
  width: 100%;
  padding: 6px 17px;
  clear: both;
  font-weight: 500;
  color: #0e1726;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  font-size: 13px;
}

.dropdown-divider {
  border-top: 1px solid #e0e6ed;
}

.btn-group .dropdown-menu a.dropdown-item:hover {
  color: #2196f3;
}
.btn-group .dropdown-menu a.dropdown-item svg {
  cursor: pointer;
  color: #888ea8;
  margin-right: 6px;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  fill: rgba(0, 23, 55, 0.08);
}
.btn-group .dropdown-menu a.dropdown-item:hover svg {
  color: #4361ee;
}

.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu {
  border: 1px solid #e0e6ed;
  z-index: 899;
  box-shadow: none;
  padding: 10px;
  padding: 0.35rem 0;
  transition: top 0.3s ease-in-out 0s, opacity 0.3s ease-in-out 0s, visibility 0.3s ease-in-out 0s;
  opacity: 0;
  visibility: hidden;
  display: block !important;
  transform: none !important;
  top: 0 !important;
  border-radius: 8px;
  background: #fff;
  box-shadow: none;
}
.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.right {
  right: auto;
  left: auto !important;
}
.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.left {
  inset: 0 0 auto auto !important;
}
.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  top: 21px !important;
}
.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item {
  border-radius: 5px;
  display: block;
  width: 100%;
  padding: 6px 17px;
  clear: both;
  font-weight: 500;
  color: #0e1726;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  font-size: 13px;
}
.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item svg {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  vertical-align: bottom;
  color: #888ea8;
}
.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item:hover svg {
  color: #2196f3;
}
.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item.active, .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item:active {
  background-color: transparent;
  color: #4361ee;
  font-weight: 700;
}
.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu a.dropdown-item:hover {
  color: #2196f3;
  background: rgb(248, 248, 248);
}

.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-success.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-info:not(:disabled):not(.disabled).active:focus, .btn-info:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-info.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-danger:not(:disabled):not(.disabled).active:focus, .btn-danger:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-danger.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-warning:not(:disabled):not(.disabled).active:focus, .btn-warning:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-warning.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-dark:not(:disabled):not(.disabled).active:focus, .btn-dark:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-dark.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-outline-primary:not(:disabled):not(.disabled).active:focus, .btn-outline-primary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-outline-success:not(:disabled):not(.disabled).active:focus, .btn-outline-success:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-outline-info:not(:disabled):not(.disabled).active:focus, .btn-outline-info:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-outline-danger:not(:disabled):not(.disabled).active:focus, .btn-outline-danger:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-outline-warning:not(:disabled):not(.disabled).active:focus, .btn-outline-warning:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-outline-secondary:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: none;
}

.btn-outline-dark:not(:disabled):not(.disabled).active:focus, .btn-outline-dark:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}

.show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: none;
}

.btn.focus, .btn:focus {
  box-shadow: none;
}

.btn-success:focus, .btn-info:focus, .btn-danger:focus, .btn-warning:focus, .btn-secondary:focus, .btn-dark:focus, .btn-outline-success:focus, .btn-outline-info:focus, .btn-outline-danger:focus, .btn-outline-warning:focus, .btn-outline-secondary:focus, .btn-outline-dark:focus .btn-light-default:focus, .btn-light-primary:focus, .btn-light-success:focus, .btn-light-info:focus, .btn-light-danger:focus, .btn-light-warning:focus, .btn-light-secondary:focus, .btn-light-dark:focus {
  box-shadow: none;
}

/*      Default Buttons       */
.btn-primary {
  color: #fff !important;
  background-color: #4361ee !important;
  border-color: #4361ee;
  box-shadow: 0 10px 20px -10px rgba(27, 85, 226, 0.59);
}
.btn-primary:hover, .btn-primary:focus {
  color: #fff !important;
  background-color: #4361ee !important;
  box-shadow: none;
  border-color: #4361ee !important;
}
.btn-primary:active, .btn-primary.active {
  background-color: #4361ee;
  border-top: 1px solid #4361ee;
}
.btn-primary.disabled, .btn-primary.btn[disabled], .btn-primary:disabled {
  background-color: #4361ee;
  border-color: #4361ee;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn-primary.active.focus, .btn-primary.active:focus, .btn-primary.active:hover {
  color: #fff !important;
  background-color: #2aebcb;
  border-color: #2aebcb;
}
.btn-primary.focus:active {
  color: #fff !important;
  background-color: #2aebcb;
  border-color: #2aebcb;
}
.btn-primary:active:focus, .btn-primary:active:hover {
  color: #fff !important;
  background-color: #2aebcb;
  border-color: #2aebcb;
}
.btn-primary:first-child:hover {
  color: #fff;
  background-color: #2e46b6;
  border-color: #2e46b6;
}

.open > .dropdown-toggle.btn-primary.focus, .open > .dropdown-toggle.btn-primary:focus, .open > .dropdown-toggle.btn-primary:hover {
  color: #fff !important;
  background-color: #2aebcb;
  border-color: #2aebcb;
}

.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #4361ee;
  border-color: #4361ee;
}

.show > .btn-primary.dropdown-toggle {
  color: #fff !important;
  background-color: #4361ee;
  border-color: #4361ee;
}

.btn-primary .caret {
  border-top-color: #fff;
}

.btn-group.open .btn-primary.dropdown-toggle {
  background-color: #bfc1fb;
}

.btn-secondary {
  color: #fff !important;
  background-color: #805dca;
  border-color: #805dca;
  box-shadow: 0 10px 20px -10px rgba(92, 26, 195, 0.59);
}
.btn-secondary:hover, .btn-secondary:focus {
  color: #fff !important;
  background-color: #805dca !important;
  box-shadow: none;
  border-color: #805dca !important;
}
.btn-secondary:active, .btn-secondary.active {
  background-color: #805dca;
  border-top: 1px solid #805dca;
}
.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #805dca;
  border-color: #805dca;
}

.show > .btn-secondary.dropdown-toggle {
  color: #fff !important;
  background-color: #805dca;
  border-color: #805dca;
}

.btn-secondary.disabled, .btn-secondary.btn[disabled], .btn-secondary:disabled {
  background-color: #805dca;
  border-color: #805dca;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn-secondary .caret {
  border-top-color: #fff;
}

.btn-info {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
  box-shadow: 0 10px 20px -10px rgba(33, 150, 243, 0.59);
}
.btn-info:hover, .btn-info:focus {
  color: #fff !important;
  background-color: #2196f3 !important;
  box-shadow: none;
  border-color: #2196f3 !important;
}
.btn-info:active, .btn-info.active {
  background-color: #2196f3;
  border-top: 1px solid #2196f3;
}
.btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}

.show > .btn-info.dropdown-toggle {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}

.btn-info.disabled, .btn-info.btn[disabled], .btn-info:disabled {
  background-color: #2196f3;
  border-color: #2196f3;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.btn.disabled, .btn:disabled {
  opacity: 0.35;
}

fieldset:disabled .btn {
  opacity: 0.35;
}

.btn-info.active.focus, .btn-info.active:focus, .btn-info.active:hover {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}
.btn-info.focus:active {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}
.btn-info:active:focus, .btn-info:active:hover {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}

.open > .dropdown-toggle.btn-info.focus, .open > .dropdown-toggle.btn-info:focus, .open > .dropdown-toggle.btn-info:hover {
  color: #fff !important;
  background-color: #2196f3;
  border-color: #2196f3;
}

.btn-info .caret {
  border-top-color: #fff;
}

.btn-group.open .btn-info.dropdown-toggle {
  background-color: #a6d5fa;
}

.btn-warning {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
  box-shadow: 0 10px 20px -10px rgba(226, 160, 63, 0.59);
}
.btn-warning:hover, .btn-warning:focus {
  color: #fff !important;
  background-color: #e2a03f !important;
  box-shadow: none;
  border-color: #e2a03f !important;
}
.btn-warning:active, .btn-warning.active {
  background-color: #e2a03f;
  border-top: 1px solid #e2a03f;
}
.btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabled):not(.disabled):active {
  color: #0e1726;
  background-color: #e2a03f;
  border-color: #e2a03f;
}

.show > .btn-warning.dropdown-toggle {
  color: #0e1726;
  background-color: #e2a03f;
  border-color: #e2a03f;
}

.btn-warning.disabled, .btn-warning.btn[disabled], .btn-warning:disabled {
  background-color: #e2a03f;
  border-color: #e2a03f;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn-warning.active.focus, .btn-warning.active:focus, .btn-warning.active:hover {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
.btn-warning.focus:active {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}
.btn-warning:active:focus, .btn-warning:active:hover {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}

.open > .dropdown-toggle.btn-warning.focus, .open > .dropdown-toggle.btn-warning:focus, .open > .dropdown-toggle.btn-warning:hover {
  color: #fff !important;
  background-color: #e2a03f;
  border-color: #e2a03f;
}

.btn-warning .caret {
  border-top-color: #fff;
}

.btn-group.open .btn-warning.dropdown-toggle {
  background-color: #df8505;
}

.btn-danger {
  color: #fff !important;
  background-color: #e7515a;
  border-color: #e7515a;
  box-shadow: 0 10px 20px -10px rgba(231, 81, 90, 0.59);
}
.btn-danger:hover, .btn-danger:focus {
  color: #fff !important;
  background-color: #e7515a !important;
  box-shadow: none;
  border-color: #e7515a !important;
}
.btn-danger:active, .btn-danger.active {
  background-color: #e7515a;
  border-top: 1px solid #e7515a;
}
.btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #e7515a;
  border-color: #e7515a;
}

.show > .btn-danger.dropdown-toggle {
  color: #fff !important;
  background-color: #e7515a;
  border-color: #e7515a;
}

.btn-danger.disabled, .btn-danger.btn[disabled], .btn-danger:disabled {
  background-color: #e7515a;
  border-color: #e7515a;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn-danger.active.focus, .btn-danger.active:focus, .btn-danger.active:hover {
  color: #fff !important;
  background-color: #c00;
  border-color: #c00;
}
.btn-danger.focus:active {
  color: #fff !important;
  background-color: #c00;
  border-color: #c00;
}
.btn-danger:active:focus, .btn-danger:active:hover {
  color: #fff !important;
  background-color: #c00;
  border-color: #c00;
}

.open > .dropdown-toggle.btn-danger.focus, .open > .dropdown-toggle.btn-danger:focus, .open > .dropdown-toggle.btn-danger:hover {
  color: #fff !important;
  background-color: #c00;
  border-color: #c00;
}

.btn-danger .caret {
  border-top-color: #fff;
}

.btn-group.open .btn-danger.dropdown-toggle {
  background-color: #a9302a;
}

.btn-dark {
  color: #fff !important;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
  box-shadow: 0 10px 20px -10px rgba(59, 63, 92, 0.59);
}
.btn-dark:hover, .btn-dark:focus {
  color: #fff !important;
  background-color: #3b3f5c !important;
  box-shadow: none;
  border-color: #3b3f5c !important;
}
.btn-dark:active, .btn-dark.active {
  background-color: #3b3f5c;
  border-top: 1px solid #3b3f5c;
}
.btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}

.show > .btn-dark.dropdown-toggle {
  color: #fff !important;
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}

.btn-dark.disabled, .btn-dark.btn[disabled], .btn-dark:disabled {
  background-color: #3b3f5c;
  border-color: #3b3f5c;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn-dark .caret {
  border-top-color: #fff;
}

.btn-group.open .btn-dark.dropdown-toggle {
  background-color: #484848;
}

.btn-success {
  color: #fff !important;
  background-color: #00ab55;
  border-color: #00ab55;
  box-shadow: 0 10px 20px -10px rgba(0, 171, 85, 0.59);
}
.btn-success:hover, .btn-success:focus {
  color: #fff !important;
  background-color: #00ab55 !important;
  box-shadow: none;
  border-color: #00ab55 !important;
}
.btn-success:active, .btn-success.active {
  background-color: #00ab55;
  border-top: 1px solid #00ab55;
}
.btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabled):not(.disabled):active {
  color: #fff !important;
  background-color: #00ab55;
  border-color: #00ab55;
}

.show > .btn-success.dropdown-toggle {
  color: #fff !important;
  background-color: #00ab55;
  border-color: #00ab55;
}

.btn-success.disabled, .btn-success.btn[disabled], .btn-success:disabled {
  background-color: #00ab55;
  border-color: #00ab55;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.btn-success.active.focus, .btn-success.active:focus, .btn-success.active:hover {
  color: #fff !important;
  background-color: #17c678;
  border-color: #17c678;
}
.btn-success.focus:active {
  color: #fff !important;
  background-color: #17c678;
  border-color: #17c678;
}
.btn-success:active:focus, .btn-success:active:hover {
  color: #fff !important;
  background-color: #17c678;
  border-color: #17c678;
}

.open > .dropdown-toggle.btn-success.focus, .open > .dropdown-toggle.btn-success:focus, .open > .dropdown-toggle.btn-success:hover {
  color: #fff !important;
  background-color: #17c678;
  border-color: #17c678;
}

.btn-success .caret {
  border-top-color: #fff;
}

.btn.box-shadow-none {
  border: none;
}
.btn.box-shadow-none:hover, .btn.box-shadow-none:focus {
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
}

.box-shadow-none {
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
}

.btn.box-shadow-none:not(:disabled):not(.disabled).active, .btn.box-shadow-none:not(:disabled):not(.disabled):active {
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
}

.show > .btn.box-shadow-none.dropdown-toggle {
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
}

.btn-group.open .btn-success.dropdown-toggle {
  background-color: #499249;
}

.btn-dismiss {
  color: #0e1726;
  background-color: #fff !important;
  border-color: #fff;
  padding: 3px 7px;
}
.btn-dismiss:hover, .btn-dismiss:focus {
  color: #0e1726;
  background-color: #fff;
}
.btn-dismiss:active, .btn-dismiss.active {
  background-color: #fff;
  border-top: 1px solid #fff;
}

.btn-group > .btn i {
  margin-right: 3px;
}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn + .dropdown-toggle {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.btn-group-vertical > .btn-check:checked + .btn, .btn-group-vertical > .btn-check:focus + .btn {
  -webkit-transform: none;
  transform: none;
  transition: 0.1s;
}
.btn-group-vertical > .btn.active, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:hover {
  -webkit-transform: none;
  transform: none;
  transition: 0.1s;
}

.btn-group > .btn-check:checked + .btn, .btn-group > .btn-check:focus + .btn {
  -webkit-transform: none;
  transform: none;
  transition: 0.1s;
}
.btn-group > .btn.active, .btn-group > .btn:active, .btn-group > .btn:focus, .btn-group > .btn:hover {
  -webkit-transform: none;
  transform: none;
  transition: 0.1s;
}

.btn-group-vertical > .btn:active {
  box-shadow: none;
}

.btn-group > .btn:hover {
  opacity: 0.8;
}

.btn-group-vertical > .btn-group:not(:first-child) {
  margin-bottom: 0;
}
.btn-group-vertical > .btn:not(:first-child) {
  margin-bottom: 0;
}
.btn-group-vertical > .btn:hover {
  opacity: 0.8;
}

/*
    Btn group dropdown-toggle
*/
.btn-group > .btn + .dropdown-toggle.btn-primary {
  border-left: 1px solid rgb(93, 119, 243);
}
.btn-group > .btn + .dropdown-toggle.btn-success {
  border-left: 1px solid rgb(74, 203, 138);
}
.btn-group > .btn + .dropdown-toggle.btn-info {
  border-left: 1px solid rgb(73, 172, 251);
}
.btn-group > .btn + .dropdown-toggle.btn-warning {
  border-left: 1px solid rgb(245, 180, 85);
}
.btn-group > .btn + .dropdown-toggle.btn-danger {
  border-left: 1px solid rgb(241, 132, 139);
}
.btn-group > .btn + .dropdown-toggle.btn-dark {
  border-left: 1px solid rgb(74, 78, 106);
}
.btn-group > .btn + .dropdown-toggle.btn-secondary {
  border-left: 1px solid rgb(149, 112, 227);
}
.btn-group.dropstart .dropdown-toggle-split {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group.dropstart .btn-primary:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(68, 104, 253);
}
.btn-group.dropstart .btn-success:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(163, 198, 111);
}
.btn-group.dropstart .btn-info:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(73, 172, 251);
}
.btn-group.dropstart .btn-warning:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(245, 180, 85);
}
.btn-group.dropstart .btn-danger:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(241, 132, 139);
}
.btn-group.dropstart .btn-dark:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(112, 118, 122);
}
.btn-group.dropstart .btn-secondary:not(.dropdown-toggle-split) {
  border-left: 1px solid rgb(131, 83, 220);
}

.btn .badge.badge-align-right {
  position: absolute;
  top: -1px;
  right: 8px;
}

.dropup .btn .caret {
  border-bottom-color: #0e1726;
}

.btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active {
  background-color: #4361ee;
  color: #fff !important;
  box-shadow: none;
}
.btn-outline-primary.dropdown-toggle.show:focus {
  background-color: #4361ee;
  color: #fff !important;
  box-shadow: none;
}

.btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):active {
  background-color: #00ab55;
  color: #fff !important;
  box-shadow: none;
}
.btn-outline-success.dropdown-toggle.show:focus {
  background-color: #00ab55;
  color: #fff !important;
  box-shadow: none;
}

.btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):active {
  background-color: #2196f3;
  color: #fff !important;
  box-shadow: none;
}
.btn-outline-info.dropdown-toggle.show:focus {
  background-color: #2196f3;
  color: #fff !important;
  box-shadow: none;
}

.btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):active {
  background-color: #e7515a;
  color: #fff !important;
  box-shadow: none;
}
.btn-outline-danger.dropdown-toggle.show:focus {
  background-color: #e7515a;
  color: #fff !important;
  box-shadow: none;
}

.btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):active {
  background-color: #e2a03f;
  color: #fff !important;
  box-shadow: none;
}
.btn-outline-warning.dropdown-toggle.show:focus {
  background-color: #e2a03f;
  color: #fff !important;
  box-shadow: none;
}

.btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active {
  background-color: #805dca;
  color: #fff !important;
  box-shadow: none;
}
.btn-outline-secondary.dropdown-toggle.show:focus {
  background-color: #805dca;
  color: #fff !important;
  box-shadow: none;
}

.btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):active {
  background-color: #3b3f5c;
  color: #fff !important;
  box-shadow: none;
}
.btn-outline-dark.dropdown-toggle.show:focus {
  background-color: #3b3f5c;
  color: #fff !important;
  box-shadow: none;
}

.show > .btn-outline-primary.dropdown-toggle:after, .show > .btn-outline-success.dropdown-toggle:after, .show > .btn-outline-info.dropdown-toggle:after, .show > .btn-outline-danger.dropdown-toggle:after, .show > .btn-outline-warning.dropdown-toggle:after, .show > .btn-outline-secondary.dropdown-toggle:after, .show > .btn-outline-dark.dropdown-toggle:after, .show > .btn-outline-primary.dropdown-toggle:before, .show > .btn-outline-success.dropdown-toggle:before, .show > .btn-outline-info.dropdown-toggle:before, .show > .btn-outline-danger.dropdown-toggle:before, .show > .btn-outline-warning.dropdown-toggle:before, .show > .btn-outline-secondary.dropdown-toggle:before, .show > .btn-outline-dark.dropdown-toggle:before {
  color: #fff !important;
}

.btn-outline-primary {
  border: 1px solid #4361ee !important;
  color: #4361ee !important;
  background-color: transparent;
  box-shadow: none;
}

.btn-outline-info {
  border: 1px solid #2196f3 !important;
  color: #2196f3 !important;
  background-color: transparent;
  box-shadow: none;
}

.btn-outline-warning {
  border: 1px solid #e2a03f !important;
  color: #e2a03f !important;
  background-color: transparent;
  box-shadow: none;
}

.btn-outline-success {
  border: 1px solid #00ab55 !important;
  color: #00ab55 !important;
  background-color: transparent;
  box-shadow: none;
}

.btn-outline-danger {
  border: 1px solid #e7515a !important;
  color: #e7515a !important;
  background-color: transparent;
  box-shadow: none;
}

.btn-outline-secondary {
  border: 1px solid #805dca !important;
  color: #805dca !important;
  background-color: transparent;
  box-shadow: none;
}

.btn-outline-dark {
  border: 1px solid #3b3f5c !important;
  color: #3b3f5c !important;
  background-color: transparent;
  box-shadow: none;
}
.btn-outline-dark.disabled, .btn-outline-dark:disabled {
  color: #3b3f5c !important;
}

.btn-outline-primary:hover, .btn-outline-info:hover, .btn-outline-warning:hover, .btn-outline-success:hover, .btn-outline-danger:hover, .btn-outline-secondary:hover, .btn-outline-dark:hover {
  box-shadow: 0px 5px 20px 0 rgba(0, 0, 0, 0.1);
}

.btn-outline-primary:hover {
  color: #fff !important;
  background-color: #4361ee !important;
  box-shadow: 0 10px 20px -10px rgba(27, 85, 226, 0.59) !important;
}

.btn-outline-info:hover {
  color: #fff !important;
  background-color: #2196f3 !important;
  box-shadow: 0 10px 20px -10px rgba(33, 150, 243, 0.588) !important;
}

.btn-outline-warning:hover {
  color: #fff !important;
  background-color: #e2a03f !important;
  box-shadow: 0 10px 20px -10px rgba(226, 160, 63, 0.588) !important;
}

.btn-outline-success:hover {
  color: #fff !important;
  background-color: #00ab55 !important;
  box-shadow: 0 10px 20px -10px rgba(0, 171, 85, 0.59) !important;
}

.btn-outline-danger:hover {
  color: #fff !important;
  background-color: #e7515a !important;
  box-shadow: 0 10px 20px -10px rgba(231, 81, 90, 0.588) !important;
}

.btn-outline-secondary:hover {
  color: #fff !important;
  background-color: #805dca !important;
  box-shadow: 0 10px 20px -10px rgba(92, 26, 195, 0.59) !important;
}

.btn-outline-dark:hover {
  color: #fff !important;
  background-color: #3b3f5c !important;
  box-shadow: 0 10px 20px -10px rgba(59, 63, 92, 0.59) !important;
}

.btn-check:active + .btn-outline-primary, .btn-check:checked + .btn-outline-primary {
  background-color: #4361ee !important;
  color: #fff !important;
}

.btn-outline-primary.active, .btn-outline-primary.dropdown-toggle.show, .btn-outline-primary:active {
  background-color: #4361ee !important;
  color: #fff !important;
}

.btn-check:active + .btn-outline-info, .btn-check:checked + .btn-outline-info {
  background-color: #2196f3 !important;
  color: #fff !important;
}

.btn-outline-info.active, .btn-outline-info.dropdown-toggle.show, .btn-outline-info:active {
  background-color: #2196f3 !important;
  color: #fff !important;
}

.btn-check:active + .btn-outline-success, .btn-check:checked + .btn-outline-success {
  background-color: #00ab55 !important;
  color: #fff !important;
}

.btn-outline-success.active, .btn-outline-success.dropdown-toggle.show, .btn-outline-success:active {
  background-color: #00ab55 !important;
  color: #fff !important;
}

.btn-check:active + .btn-outline-warning, .btn-check:checked + .btn-outline-warning {
  background-color: #e2a03f !important;
  color: #fff !important;
}

.btn-outline-warning.active, .btn-outline-warning.dropdown-toggle.show, .btn-outline-warning:active {
  background-color: #e2a03f !important;
  color: #fff !important;
}

.btn-check:active + .btn-outline-danger, .btn-check:checked + .btn-outline-danger {
  background-color: #e7515a !important;
  color: #fff !important;
}

.btn-outline-danger.active, .btn-outline-danger.dropdown-toggle.show, .btn-outline-danger:active {
  background-color: #e7515a !important;
  color: #fff !important;
}

.btn-check:active + .btn-outline-secondary, .btn-check:checked + .btn-outline-secondary {
  background-color: #805dca !important;
  color: #fff !important;
}

.btn-outline-secondary.active, .btn-outline-secondary.dropdown-toggle.show, .btn-outline-secondary:active {
  background-color: #805dca !important;
  color: #fff !important;
}

.btn-check:active + .btn-outline-dark, .btn-check:checked + .btn-outline-dark {
  background-color: #3b3f5c !important;
  color: #fff !important;
}

.btn-outline-dark.active, .btn-outline-dark.dropdown-toggle.show, .btn-outline-dark:active {
  background-color: #3b3f5c !important;
  color: #fff !important;
}

/* Primary */
.btn-check:active + .btn-outline-primary:focus, .btn-check:checked + .btn-outline-primary:focus {
  box-shadow: none;
}

.btn-outline-primary.active:focus, .btn-outline-primary.dropdown-toggle.show:focus, .btn-outline-primary:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-outline-primary, .btn-outline-primary:focus {
  box-shadow: none;
}

.btn-check:active + .btn-outline-info:focus, .btn-check:checked + .btn-outline-info:focus {
  box-shadow: none;
}

.btn-outline-info.active:focus, .btn-outline-info.dropdown-toggle.show:focus, .btn-outline-info:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-outline-info, .btn-outline-info:focus {
  box-shadow: none;
}

.btn-check:active + .btn-outline-success:focus, .btn-check:checked + .btn-outline-success:focus {
  box-shadow: none;
}

.btn-outline-success.active:focus, .btn-outline-success.dropdown-toggle.show:focus, .btn-outline-success:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-outline-success, .btn-outline-success:focus {
  box-shadow: none;
}

.btn-check:active + .btn-outline-danger:focus, .btn-check:checked + .btn-outline-danger:focus {
  box-shadow: none;
}

.btn-outline-danger.active:focus, .btn-outline-danger.dropdown-toggle.show:focus, .btn-outline-danger:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-outline-danger, .btn-outline-danger:focus {
  box-shadow: none;
}

.btn-check:active + .btn-outline-secondary:focus, .btn-check:checked + .btn-outline-secondary:focus {
  box-shadow: none;
}

.btn-outline-secondary.active:focus, .btn-outline-secondary.dropdown-toggle.show:focus, .btn-outline-secondary:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-outline-secondary, .btn-outline-secondary:focus {
  box-shadow: none;
}

.btn-check:active + .btn-outline-warning:focus, .btn-check:checked + .btn-outline-warning:focus {
  box-shadow: none;
}

.btn-outline-warning.active:focus, .btn-outline-warning.dropdown-toggle.show:focus, .btn-outline-warning:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-outline-warning, .btn-outline-warning:focus {
  box-shadow: none;
}

.btn-check:active + .btn-outline-dark:focus, .btn-check:checked + .btn-outline-dark:focus {
  box-shadow: none;
}

.btn-outline-dark.active:focus, .btn-outline-dark.dropdown-toggle.show:focus, .btn-outline-dark:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-outline-dark, .btn-outline-dark:focus {
  box-shadow: none;
}

/* Light Buttons  */
[class*=btn-light-] {
  box-shadow: none;
}

.btn-light-primary {
  color: #4361ee;
  background-color: #eceffe;
  border: 1px solid #eceffe;
}
.btn-light-primary:hover {
  background-color: #eceffe !important;
  border: 1px solid #eceffe !important;
  color: #4361ee !important;
}

.btn-light-info {
  color: #2196f3;
  background-color: #e6f4ff;
  border: 1px solid #e6f4ff;
}
.btn-light-info:hover {
  background-color: #e6f4ff !important;
  border: 1px solid #e6f4ff !important;
  color: #2196f3 !important;
}

.btn-light-warning {
  color: #eab764;
  background-color: #fcf5e9;
  border: 1px solid #fcf5e9;
}
.btn-light-warning:hover {
  background-color: #fcf5e9 !important;
  border: 1px solid #fcf5e9 !important;
  color: #eab764 !important;
}

.btn-light-success {
  color: #00ab55;
  background-color: #ddf5f0;
  border: 1px solid #ddf5f0;
}
.btn-light-success:hover {
  background-color: #ddf5f0 !important;
  border: 1px solid #ddf5f0 !important;
  color: #00ab55 !important;
}

.btn-light-danger {
  color: #e7515a;
  background-color: #fbeced;
  border: 1px solid #fbeced;
}
.btn-light-danger:hover {
  background-color: #fbeced !important;
  border: 1px solid #fbeced !important;
  color: #e7515a !important;
}

.btn-light-secondary {
  color: #805dca;
  background-color: #f2eafa;
  border: 1px solid #f2eafa;
}
.btn-light-secondary:hover {
  background-color: #f2eafa !important;
  border: 1px solid #f2eafa !important;
  color: #805dca !important;
}

.btn-light-dark {
  color: #3b3f5c;
  background-color: #eaeaec;
  border: 1px solid #eaeaec;
}
.btn-light-dark:hover {
  background-color: #eaeaec !important;
  border: 1px solid #eaeaec !important;
  color: #3b3f5c !important;
}

.btn-check:active + .btn-light-primary, .btn-check:checked + .btn-light-primary {
  background-color: #4361ee !important;
  color: #fff !important;
}

.btn-light-primary.dropdown-toggle.show {
  background-color: #4361ee !important;
  color: #fff !important;
}

.btn-check:active + .btn-light-info, .btn-check:checked + .btn-light-info {
  background-color: #2196f3 !important;
  color: #fff !important;
}

.btn-light-info.dropdown-toggle.show {
  background-color: #2196f3 !important;
  color: #fff !important;
}

.btn-check:active + .btn-light-success, .btn-check:checked + .btn-light-success {
  background-color: #00ab55 !important;
  color: #fff !important;
}

.btn-light-success.dropdown-toggle.show {
  background-color: #00ab55 !important;
  color: #fff !important;
}

.btn-check:active + .btn-light-warning, .btn-check:checked + .btn-light-warning {
  background-color: #e2a03f !important;
  color: #fff !important;
}

.btn-light-warning.dropdown-toggle.show {
  background-color: #e2a03f !important;
  color: #fff !important;
}

.btn-check:active + .btn-light-danger, .btn-check:checked + .btn-light-danger {
  background-color: #e7515a !important;
  color: #fff !important;
}

.btn-light-danger.dropdown-toggle.show {
  background-color: #e7515a !important;
  color: #fff !important;
}

.btn-check:active + .btn-light-secondary, .btn-check:checked + .btn-light-secondary {
  background-color: #805dca !important;
  color: #fff !important;
}

.btn-light-secondary.dropdown-toggle.show {
  background-color: #805dca !important;
  color: #fff !important;
}

.btn-check:active + .btn-light-dark, .btn-check:checked + .btn-light-dark {
  background-color: #3b3f5c !important;
  color: #fff !important;
}

.btn-light-dark.dropdown-toggle.show {
  background-color: #3b3f5c !important;
  color: #fff !important;
}

/* Primary */
.btn-check:active + .btn-light-primary:focus, .btn-check:checked + .btn-light-primary:focus {
  box-shadow: none;
}

.btn-light-primary.active:focus, .btn-light-primary.dropdown-toggle.show:focus, .btn-light-primary:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-light-primary, .btn-light-primary:focus {
  box-shadow: none;
}

.btn-check:active + .btn-light-info:focus, .btn-check:checked + .btn-light-info:focus {
  box-shadow: none;
}

.btn-light-info.active:focus, .btn-light-info.dropdown-toggle.show:focus, .btn-light-info:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-light-info, .btn-light-info:focus {
  box-shadow: none;
}

.btn-check:active + .btn-light-success:focus, .btn-check:checked + .btn-light-success:focus {
  box-shadow: none;
}

.btn-light-success.active:focus, .btn-light-success.dropdown-toggle.show:focus, .btn-light-success:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-light-success, .btn-light-success:focus {
  box-shadow: none;
}

.btn-check:active + .btn-light-danger:focus, .btn-check:checked + .btn-light-danger:focus {
  box-shadow: none;
}

.btn-light-danger.active:focus, .btn-light-danger.dropdown-toggle.show:focus, .btn-light-danger:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-light-danger, .btn-light-danger:focus {
  box-shadow: none;
}

.btn-check:active + .btn-light-secondary:focus, .btn-check:checked + .btn-light-secondary:focus {
  box-shadow: none;
}

.btn-light-secondary.active:focus, .btn-light-secondary.dropdown-toggle.show:focus, .btn-light-secondary:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-light-secondary, .btn-light-secondary:focus {
  box-shadow: none;
}

.btn-check:active + .btn-light-warning:focus, .btn-check:checked + .btn-light-warning:focus {
  box-shadow: none;
}

.btn-light-warning.active:focus, .btn-light-warning.dropdown-toggle.show:focus, .btn-light-warning:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-light-warning, .btn-light-warning:focus {
  box-shadow: none;
}

.btn-check:active + .btn-light-dark:focus, .btn-check:checked + .btn-light-dark:focus {
  box-shadow: none;
}

.btn-light-dark.active:focus, .btn-light-dark.dropdown-toggle.show:focus, .btn-light-dark:active:focus {
  box-shadow: none;
}

.btn-check:focus + .btn-light-dark, .btn-light-dark:focus {
  box-shadow: none;
}

/*      Dropdown Toggle       */
.btn-rounded {
  -webkit-border-radius: 1.875rem;
  -moz-border-radius: 1.875rem;
  -ms-border-radius: 1.875rem;
  -o-border-radius: 1.875rem;
  border-radius: 1.875rem;
}

/*
    ===========================
        Checkboxes and Radio
    ===========================
*/
.form-check.form-check-primary .form-check-input:checked {
  background-color: #4361ee;
  border-color: #4361ee;
}
.form-check.form-check-success .form-check-input:checked {
  background-color: #00ab55;
  border-color: #00ab55;
}
.form-check.form-check-danger .form-check-input:checked {
  background-color: #e7515a;
  border-color: #e7515a;
}
.form-check.form-check-secondary .form-check-input:checked {
  background-color: #805dca;
  border-color: #805dca;
}
.form-check.form-check-warning .form-check-input:checked {
  background-color: #e2a03f;
  border-color: #e2a03f;
}
.form-check.form-check-info .form-check-input:checked {
  background-color: #2196f3;
  border-color: #2196f3;
}
.form-check.form-check-dark .form-check-input:checked {
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}

/*
    =================
        Switches
    =================
*/
.form-switch .form-check-input {
  /* width: 2em; */
  width: 35px;
  height: 18px;
}
.form-switch .form-check-input:focus {
  border-color: transparent;
}
.form-switch .form-check-input:not(:checked):focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
}
.form-switch .form-check-label {
  margin-left: 8px;
  vertical-align: text-top;
}
.form-switch.form-switch-primary .form-check-input:checked {
  background-color: #4361ee;
  border-color: #4361ee;
}
.form-switch.form-switch-success .form-check-input:checked {
  background-color: #00ab55;
  border-color: #00ab55;
}
.form-switch.form-switch-danger .form-check-input:checked {
  background-color: #e7515a;
  border-color: #e7515a;
}
.form-switch.form-switch-secondary .form-check-input:checked {
  background-color: #805dca;
  border-color: #805dca;
}
.form-switch.form-switch-warning .form-check-input:checked {
  background-color: #e2a03f;
  border-color: #e2a03f;
}
.form-switch.form-switch-info .form-check-input:checked {
  background-color: #2196f3;
  border-color: #2196f3;
}
.form-switch.form-switch-dark .form-check-input:checked {
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}

/*
    ===========================
        Data Marker ( dot )
    ===========================
*/
.data-marker {
  padding: 2px;
  border-radius: 50%;
  font-size: 18px;
  display: inline-flex;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}

.data-marker-success {
  background-color: #00ab55;
}

.data-marker-warning {
  background-color: #e2a03f;
}

.data-marker-danger, .data-marker-info, .data-marker-dark {
  background-color: #e7515a;
}

.badge {
  font-weight: 600;
  line-height: 1.4;
  font-size: 11.9px;
  font-weight: 600;
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  display: inline-block;
  padding: 4.6px 8px;
  color: #FFF;
  border-radius: 6px;
}
.badge:hover {
  transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  -webkit-transform: translateY(-3px);
  transform: translateY(-3px);
}

.badge--group {
  display: inline-flex;
}
.badge--group .badge {
  border: 2px solid #e0e6ed;
}
.badge--group .badge:not(:first-child) {
  margin-left: -6px;
}

.badge-dot:empty {
  display: block;
}

.badge--group .badge-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  padding: 0;
}

.badge svg {
  width: 15px;
  height: 15px;
  vertical-align: top;
  margin-right: 3px;
}
.badge.badge-enabled {
  background-color: #00ab55;
  color: #fff;
}
.badge.badge-disable {
  background-color: #e7515a;
  color: #fff;
}

.badge-collapsed-img img {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid #515365;
  margin-left: -21px;
}
.badge-collapsed-img.badge-tooltip img {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid #ffffff;
  box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.3);
  margin-left: -21px;
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
.badge-collapsed-img.badge-tooltip img:hover {
  -webkit-transform: translateY(-5px) scale(1.02);
  transform: translateY(-5px) scale(1.02);
}
.badge-collapsed-img.translateY-axis img {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
.badge-collapsed-img.translateY-axis img:hover {
  -webkit-transform: translateY(-5px) scale(1.02);
  transform: translateY(-5px) scale(1.02);
}
.badge-collapsed-img.rectangle-collapsed img {
  width: 45px;
  height: 32px;
}
.badge-collapsed-img.translateX-axis img {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
.badge-collapsed-img.translateX-axis img:hover {
  -webkit-transform: translateX(5px) scale(1.02);
  transform: translateX(5px) scale(1.02);
}

.badge-primary {
  color: #fff;
  background-color: #4361ee;
}

.badge-info {
  color: #fff;
  background-color: #2196f3;
}

.badge-success {
  color: #fff;
  background-color: #00ab55;
}

.badge-danger {
  color: #fff;
  background-color: #e7515a;
}

.badge-warning {
  color: #fff;
  background-color: #e2a03f;
}

.badge-dark {
  color: #fff;
  background-color: #3b3f5c;
}

.badge-secondary {
  background-color: #805dca;
}

.outline-badge-primary {
  color: #4361ee;
  background-color: transparent;
  border: 1px solid #4361ee;
}

.outline-badge-info {
  color: #2196f3;
  background-color: transparent;
  border: 1px solid #2196f3;
}

.outline-badge-success {
  color: #00ab55;
  background-color: transparent;
  border: 1px solid #00ab55;
}

.outline-badge-danger {
  color: #e7515a;
  background-color: transparent;
  border: 1px solid #e7515a;
}

.outline-badge-warning {
  color: #e2a03f;
  background-color: transparent;
  border: 1px solid #e2a03f;
}

.outline-badge-dark {
  color: #3b3f5c;
  background-color: transparent;
  border: 1px solid #3b3f5c;
}

.outline-badge-secondary {
  color: #805dca;
  background-color: transparent;
  border: 1px solid #805dca;
}

.outline-badge-primary:focus, .outline-badge-primary:hover {
  background-color: #4361ee;
  color: #fff;
}

.outline-badge-secondary:focus, .outline-badge-secondary:hover {
  color: #fff;
  background-color: #805dca;
}

.outline-badge-success:focus, .outline-badge-success:hover {
  color: #fff;
  background-color: #00ab55;
}

.outline-badge-danger:focus, .outline-badge-danger:hover {
  color: #fff;
  background-color: #e7515a;
}

.outline-badge-warning:focus, .outline-badge-warning:hover {
  color: #fff;
  background-color: #e2a03f;
}

.outline-badge-info:focus, .outline-badge-info:hover {
  color: #fff;
  background-color: #2196f3;
}

.outline-badge-dark:focus, .outline-badge-dark:hover {
  color: #fff;
  background-color: #3b3f5c;
}

.badge-light-primary {
  color: #4361ee;
  background-color: #eceffe;
  border: 1px solid #eceffe;
}

.badge-light-info {
  color: #2196f3;
  background-color: #e6f4ff;
  border: 1px solid #e6f4ff;
}

.badge-light-success {
  color: #00ab55;
  background-color: #ddf5f0;
  border: 1px solid #ddf5f0;
}

.badge-light-danger {
  color: #e7515a;
  background-color: #fbeced;
  border: 1px solid #fbeced;
}

.badge-light-warning {
  color: #e2a03f;
  background-color: #fcf5e9;
  border: 1px solid #fcf5e9;
}

.badge-light-dark {
  color: #3b3f5c;
  background-color: #eaeaec;
  border: 1px solid #eaeaec;
}

.badge-light-secondary {
  color: #805dca;
  background-color: #f2eafa;
  border: 1px solid #f2eafa;
}

/*      Link     */
.badge[class*=link-badge-] {
  cursor: pointer;
}

.link-badge-primary {
  color: #4361ee;
  background-color: transparent;
  border: 1px solid transparent;
}

.link-badge-info {
  color: #2196f3;
  background-color: transparent;
  border: 1px solid transparent;
}

.link-badge-success {
  color: #00ab55;
  background-color: transparent;
  border: 1px solid transparent;
}

.link-badge-danger {
  color: #e7515a;
  background-color: transparent;
  border: 1px solid transparent;
}

.link-badge-warning {
  color: #e2a03f;
  background-color: transparent;
  border: 1px solid transparent;
}

.link-badge-dark {
  color: #3b3f5c;
  background-color: transparent;
  border: 1px solid transparent;
}

.link-badge-secondary {
  color: #805dca;
  background-color: transparent;
  border: 1px solid transparent;
}

.link-badge-primary:focus, .link-badge-primary:hover {
  color: #4361ee;
  background-color: transparent;
}

.link-badge-secondary:focus, .link-badge-secondary:hover {
  color: #6f51ea;
  background-color: transparent;
}

.link-badge-success:focus, .link-badge-success:hover {
  color: #2ea37d;
  background-color: transparent;
}

.link-badge-danger:focus, .link-badge-danger:hover {
  color: #e7515a;
  background-color: transparent;
}

.link-badge-warning:focus, .link-badge-warning:hover {
  color: #dea82a;
  background-color: transparent;
}

.link-badge-info:focus, .link-badge-info:hover {
  color: #009eda;
  background-color: transparent;
}

.link-badge-dark:focus, .link-badge-dark:hover {
  color: #454656;
  background-color: transparent;
}

.avatar {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 3rem;
  font-size: 1rem;
}

.avatar--group {
  display: inline-flex;
  margin-right: 15px;
}
.avatar--group.avatar-group-badge {
  position: relative;
}
.avatar--group.avatar-group-badge .badge.counter {
  z-index: 2;
  right: 0;
  top: -6px;
  width: 21px;
  height: 21px;
  border-radius: 50%;
  padding: 5px 0px;
  font-size: 9px;
  left: -21px;
  border: none;
}
.avatar--group.avatar-group-badge .badge.counter:empty {
  display: block;
  height: 13px;
  width: 13px;
  left: -14px;
  top: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.avatar .avatar-title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #888ea8;
  color: #fff;
}
.avatar .avatar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #805dca;
  color: #fff;
}

.avatar-icon svg {
  width: 24px;
  height: 24px;
  stroke-width: 1.7;
}

.avatar--group .avatar-xl {
  margin-left: -1.28125rem;
}
.avatar--group .avatar {
  margin-left: -0.75rem;
}
.avatar--group img, .avatar--group .avatar .avatar-title {
  border: 2px solid #e0e6ed;
}

.avatar-xl {
  width: 5.125rem;
  height: 5.125rem;
  font-size: 1.70833rem;
}
.avatar-xl svg {
  width: 43px;
  height: 43px;
}

.avatar-lg {
  width: 4rem;
  height: 4rem;
  font-size: 1.33333rem;
}
.avatar-lg svg {
  width: 32px;
  height: 32px;
}

.avatar-sm {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 0.83333rem;
}
.avatar-sm svg {
  width: 18px;
  height: 18px;
}

/*
	Indicators
*/
.avatar-indicators:before {
  content: "";
  position: absolute;
  bottom: 1%;
  right: 5%;
  width: 28%;
  height: 28%;
  border-radius: 50%;
  border: 2px solid #fff;
}

.avatar-offline:before {
  background-color: #888ea8;
}

.avatar-online:before {
  background-color: #009688;
}

.avatar.translateY-axis img, .avatar.translateY-axis .avatar-title {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
.avatar.translateY-axis img:hover, .avatar.translateY-axis .avatar-title:hover {
  -webkit-transform: translateY(-5px) scale(1.02);
  transform: translateY(-5px) scale(1.02);
}
.avatar.translateX-axis img, .avatar.translateX-axis .avatar-title {
  -webkit-transition: all 0.35s ease;
  transition: all 0.35s ease;
}
.avatar.translateX-axis img:hover, .avatar.translateX-axis .avatar-title:hover {
  -webkit-transform: translateX(5px) scale(1.02);
  transform: translateX(5px) scale(1.02);
}

/*      Avatar      */
.avatar-chip {
  display: inline-block;
  padding: 0 24px;
  font-size: 16px;
  line-height: 34px;
  border-radius: 25px;
  position: relative;
  box-shadow: 0 10px 20px -10px rgba(0, 0, 0, 0.59);
}
.avatar-chip.avatar-dismiss {
  padding: 0 31px 0 25px;
}
.avatar-chip img {
  float: left;
  margin: 0px 10px 0px -26px;
  height: 35px;
  width: 35px;
  border-radius: 50%;
}
.avatar-chip span.text {
  font-size: 13px;
  font-weight: 600;
}
.avatar-chip .closebtn {
  color: #ffffff;
  font-weight: bold;
  /* float: right; */
  font-size: 15px;
  cursor: pointer;
  position: absolute;
  /* left: 0; */
  right: 8px;
}
.avatar-chip .closebtn:hover {
  color: #fff;
}

.status.rounded-tooltip .tooltip-inner {
  border-radius: 20px;
  padding: 8px 20px;
}

.tooltip-inner {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}

.popover {
  z-index: 999;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border-bottom-color: #b3b3b3;
}

.help-block, .help-inline {
  color: #555555;
}

.controls {
  position: relative;
}

/* .search-form-control { border-radius: .25rem; } */
/*  
    ====================
        Table
    ====================
*/
.table {
  color: #515365;
  border-collapse: separate;
  border-spacing: 0;
}
.table th .form-check, .table td .form-check {
  margin-right: 0;
  display: inline-flex;
  margin-bottom: 0;
}
.table .form-check-input {
  background-color: #bfc9d4;
  border-color: #bfc9d4;
}
.table thead {
  color: #515365;
  letter-spacing: 1px;
}
.table thead tr th {
  border: none;
  background: #ebedf2;
  padding: 10px 21px 10px 21px;
  vertical-align: middle;
  font-weight: 500;
}
.table thead tr.table-row-hidden {
  border: none;
}
.table:not(.dataTable) thead tr th:first-child {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
.table:not(.dataTable) thead tr th:last-child {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
.table:not(.dataTable) thead tr th.checkbox-area {
  width: 5%;
}
.table tbody {
  border: none;
}
.table tbody tr th {
  border: none;
}
.table tbody tr td {
  border: none;
  padding: 10px 21px 10px 21px;
  vertical-align: middle;
  letter-spacing: normal;
  white-space: nowrap;
  font-weight: 400;
}
.table > :not(:first-child) {
  border: none;
}
.table:not(.dataTable) tbody tr td svg {
  width: 17px;
  height: 17px;
  vertical-align: text-top;
  color: #4361ee;
  stroke-width: 1.5;
}
.table tbody tr td .table-inner-text {
  margin-left: 5px;
}
.table > tbody > tr > td .usr-img-frame {
  background-color: #e0e6ed;
  padding: 2px;
  width: 38px;
  height: 38px;
}
.table > tbody > tr > td .usr-img-frame img {
  width: 38px;
  margin: 0;
}
.table > tbody > tr > td .progress {
  width: 135px;
  height: 6px;
  margin: auto 0;
}
.table > tbody .action-btns .action-btn svg {
  width: 20px;
  height: 20px;
  color: #888ea8;
  stroke-width: 2;
}
.table > tbody .action-btns .action-btn:hover svg {
  color: #bfc9d4;
}
.table > tbody .action-btns .btn-delete svg {
  color: #f8538d;
}
.table > tbody .action-btns .btn-delete:hover svg {
  color: #e7515a;
}

/*

    Hover

*/
.table-hover > tbody > tr:hover td {
  --bs-table-accent-bg:transparent;
  color: #515365;
  background-color: #ebedf2;
  cursor: pointer;
}
.table-hover > tbody > tr:hover td:first-child {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
.table-hover > tbody > tr:hover td:last-child {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

/* 
    Hover and Striped
*/
.table-striped.table-hover > tbody > tr:hover td {
  background-color: #ebedf2;
}
.table-striped:not(.dataTable) > tbody > tr:nth-of-type(odd) td {
  --bs-table-accent-bg:transparent;
  color: #3b3f5c;
  background-color: #f1f2f3;
}
.table-striped:not(.dataTable) > tbody > tr:nth-of-type(odd) td:first-child {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
.table-striped:not(.dataTable) > tbody > tr:nth-of-type(odd) td:last-child {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

/* 

    Striped

*/
/* 
    Striped and Bordered
*/
.table:not(.dataTable).table-bordered.table-striped > tbody > tr:nth-of-type(odd) td:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.table:not(.dataTable).table-bordered.table-striped > tbody > tr:nth-of-type(odd) td:last-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.table:not(.dataTable).table-bordered.table-striped > tbody > tr:first-child td:first-child {
  border-top-left-radius: 0;
}
.table:not(.dataTable).table-bordered.table-striped > tbody > tr:first-child td:last-child {
  border-top-right-radius: 0;
}
.table:not(.dataTable).table-bordered.table-striped > tbody > tr:last-child td:first-child {
  border-bottom-left-radius: 10px;
}
.table:not(.dataTable).table-bordered.table-striped > tbody > tr:last-child td:last-child {
  border-bottom-right-radius: 10px;
}
.table:not(.dataTable).table-bordered thead tr th {
  border: 1px solid #ebedf2;
  background: transparent;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.table:not(.dataTable).table-bordered > tbody > tr td {
  border: 1px solid #ebedf2;
}
.table:not(.dataTable).table-bordered > tbody > tr:last-child td:first-child {
  border-bottom-left-radius: 10px;
}
.table:not(.dataTable).table-bordered > tbody > tr:last-child td:last-child {
  border-bottom-right-radius: 10px;
}
.table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover td:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover td:last-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover:first-child td:first-child {
  border-top-left-radius: 0;
}
.table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover:first-child td:last-child {
  border-top-right-radius: 0;
}
.table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover:last-child td:first-child {
  border-bottom-left-radius: 10px;
}
.table:not(.dataTable).table-bordered.table-hover > tbody > tr:hover:last-child td:last-child {
  border-bottom-right-radius: 10px;
}

/* 

    Bordered

*/
/* 
    Bordered and Hover
*/
.statbox .widget-content:before, .statbox .widget-content:after {
  display: table;
  content: "";
  line-height: 0;
  clear: both;
}

.nav-tabs > li > a {
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  border-radius: 0 !important;
}

.nav-link {
  color: #3b3f5c;
}
.nav-link:hover {
  color: #515365;
}
.nav-link:hover svg {
  color: #515365;
}

.btn-toolbar {
  margin-left: 0px;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .input-group > .form-control {
    flex: 1 1 auto;
    width: 1%;
  }
}
.spin {
  -webkit-animation: spin 2s infinite linear;
  animation: spin 2s infinite linear;
}

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
.toast-primary {
  background: #4361ee;
}

.toast-header {
  background: #4361ee;
  color: #fff;
  border-bottom: 1px solid rgba(33, 150, 243, 0.3411764706);
}
.toast-header .meta-time {
  color: #f1f2f3;
}
.toast-header .btn-close {
  color: #f1f2f3;
  opacity: 1;
  text-shadow: none;
  background: none;
  padding: 0;
  margin-top: -2px;
}

.toast-body {
  padding: 16px 12px;
  color: #fff;
}

/*  
    ==========================
        Background Colors  
    ==========================
*/
/*  
    Default  
*/
.bg-primary {
  background-color: #4361ee !important;
  border-color: #4361ee;
  color: #fff;
}

.bg-success {
  background-color: #00ab55 !important;
  border-color: #00ab55;
  color: #fff;
}

.bg-info {
  background-color: #2196f3 !important;
  border-color: #2196f3;
  color: #fff;
}

.bg-warning {
  background-color: #e2a03f !important;
  border-color: #e2a03f;
  color: #fff;
}

.bg-danger {
  background-color: #e7515a !important;
  border-color: #e7515a;
  color: #fff;
}

.bg-secondary {
  background-color: #805dca !important;
  border-color: #805dca;
  color: #fff;
}

.bg-dark {
  background-color: #3b3f5c !important;
  border-color: #3b3f5c;
  color: #fff;
}

/*  
    Light Background  
*/
.bg-light-primary {
  background-color: #eceffe !important;
  border-color: #eceffe;
  color: #2196f3;
}

.bg-light-success {
  background-color: #ddf5f0 !important;
  border-color: #ddf5f0;
  color: #00ab55;
}

.bg-light-info {
  background-color: #e6f4ff !important;
  border-color: #e6f4ff;
  color: #2196f3;
}

.bg-light-warning {
  background-color: #fcf5e9 !important;
  border-color: #fcf5e9;
  color: #e2a03f;
}

.bg-light-danger {
  background-color: #fbeced !important;
  border-color: #fbeced;
  color: #e7515a;
}

.bg-light-secondary {
  background-color: #f2eafa !important;
  border-color: #f2eafa;
  color: #805dca;
}

.bg-light-dark {
  background-color: #eaeaec;
  border-color: #eaeaec;
  color: #fff;
}

/*  
    Progress Bar
*/
.progress {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  background-color: #ebedf2;
  margin-bottom: 1.25rem;
  height: 16px;
  box-shadow: none;
}
.progress.progress-bar-stack .progress-bar:last-child {
  border-top-right-radius: 16px;
  border-bottom-right-radius: 16px;
}
.progress .progress-bar {
  font-size: 10px;
  font-weight: 700;
  box-shadow: 0 2px 4px rgba(0, 69, 255, 0.15), 0 8px 16px rgba(0, 69, 255, 0.2);
  font-size: 12px;
  letter-spacing: 1px;
  font-weight: 100;
}
.progress:not(.progress-bar-stack) .progress-bar {
  border-radius: 16px;
}

.progress-sm {
  height: 4px;
}

.progress-md {
  height: 10px;
}

.progress-lg {
  height: 20px;
}

.progress-xl {
  height: 25px;
}

.progress-striped .progress-bar {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress .progress-title {
  display: flex;
  justify-content: space-between;
  padding: 15px;
}
.progress .progress-title span {
  align-self: center;
}
.progress .progress-bar.bg-gradient-primary {
  background-color: #4361ee;
  background: linear-gradient(to right, #0081ff 0%, #0045ff 100%);
}
.progress .progress-bar.bg-gradient-info {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #04befe 0%, #4481eb 100%);
}
.progress .progress-bar.bg-gradient-success {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #3cba92 0%, #0ba360 100%);
}
.progress .progress-bar.bg-gradient-warning {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #f09819 0%, #ff5858 100%);
}
.progress .progress-bar.bg-gradient-secondary {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #7579ff 0%, #b224ef 100%);
}
.progress .progress-bar.bg-gradient-danger {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #d09693 0%, #c71d6f 100%);
}
.progress .progress-bar.bg-gradient-dark {
  background-color: #4361ee;
  background-image: linear-gradient(to right, #2b5876 0%, #4e4376 100%);
}

/* 
    =====================
        BreadCrumbs
    =====================
*/
.page-meta {
  margin-top: 25px;
}
.page-meta .breadcrumb .breadcrumb-item {
  font-size: 17px;
  font-weight: 500;
  letter-spacing: 1px;
}
.page-meta .breadcrumb .breadcrumb-item a {
  vertical-align: inherit;
}
.page-meta .breadcrumb .breadcrumb-item.active {
  font-weight: 500;
}

.breadcrumb {
  background-color: transparent;
  margin-bottom: 0;
}

.breadcrumb-wrapper-content {
  background-color: #f1f2f3;
  padding: 13px 23px;
  border-radius: 8px;
  box-shadow: 0 0 40px 0 rgba(94, 92, 154, 0.06);
}

.breadcrumb .breadcrumb-item a {
  color: #888ea8;
  vertical-align: text-bottom;
  vertical-align: text-top;
}
.breadcrumb .breadcrumb-item.active a {
  color: #515365;
}
.breadcrumb .breadcrumb-item a svg {
  width: 19px;
  height: 19px;
  vertical-align: sub;
  stroke-width: 1.4px;
}
.breadcrumb .breadcrumb-item a .inner-text {
  margin-left: 10px;
}
.breadcrumb .breadcrumb-item span {
  vertical-align: text-bottom;
}
.breadcrumb .breadcrumb-item.active {
  color: #3b3f5c;
  font-weight: 600;
}

/*
    Style Two
*/
.breadcrumb-style-two .breadcrumb-item + .breadcrumb-item::before {
  content: ".";
  position: relative;
  top: -9px;
  font-size: 21px;
  height: 7px;
}

/*
    Style Three
*/
.breadcrumb-style-three .breadcrumb-item + .breadcrumb-item::before {
  content: "-";
}

/*
    Style Four
*/
.breadcrumb-style-four .breadcrumb-item + .breadcrumb-item::before {
  content: "|";
}

/*
    Style Five
*/
.breadcrumb-style-five .breadcrumb-item + .breadcrumb-item::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right' style='color: %23888ea8;'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  color: #6E6B7B;
  margin-right: 0.6rem;
  background-size: 12px;
  height: 20px;
}

.br-0 {
  border-radius: 0 !important;
}

.br-4 {
  border-radius: 4px !important;
}

.br-6 {
  border-radius: 6px !important;
}

.br-8 {
  border-radius: 8px !important;
}

.br-30 {
  border-radius: 30px !important;
}

.br-50 {
  border-radius: 50px !important;
}

.br-left-30 {
  border-top-left-radius: 30px !important;
  border-bottom-left-radius: 30px !important;
}

.br-right-30 {
  border-top-right-radius: 30px !important;
  border-bottom-right-radius: 30px !important;
}

.bx-top-6 {
  border-top-right-radius: 6px !important;
  border-top-left-radius: 6px !important;
}

.bx-bottom-6 {
  border-bottom-right-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}

/*      Badge Custom      */
.badge.counter {
  position: absolute;
  z-index: 2;
  right: 0;
  top: -10px;
  font-weight: 600;
  width: 19px;
  height: 19px;
  border-radius: 50%;
  padding: 2px 0px;
  font-size: 12px;
}

/*-------text-colors------*/
.text-primary {
  color: #4361ee !important;
}

.text-success {
  color: #00ab55 !important;
}

.text-info {
  color: #2196f3 !important;
}

.text-danger {
  color: #e7515a !important;
}

.text-warning {
  color: #e2a03f !important;
}

.text-secondary {
  color: #805dca !important;
}

.text-dark {
  color: #3b3f5c !important;
}

.text-muted {
  color: #888ea8 !important;
}

.text-white {
  color: #fff !important;
}

.text-black {
  color: #000 !important;
}

/*-----border main------*/
.border {
  border: 1px solid !important;
}

.border-bottom {
  border-bottom: 1px solid !important;
}

.border-top {
  border-top: 1px solid !important;
}

.border-right {
  border-right: 1px solid !important;
}

.border-left {
  border-left: 1px solid !important;
}

.border-primary {
  border-color: #4361ee !important;
}

.border-info {
  border-color: #2196f3 !important;
}

.border-warning {
  border-color: #e2a03f !important;
}

.border-success {
  border-color: #00ab55 !important;
}

.border-danger {
  border-color: #e7515a !important;
}

.border-secondary {
  border-color: #805dca !important;
}

.border-dark {
  border-color: #3b3f5c !important;
}

/*-----border style------*/
.border-dotted {
  border-style: dotted !important;
}

.border-dashed {
  border-style: dashed !important;
}

.border-solid {
  border-style: solid !important;
}

.border-double {
  border-style: double !important;
}

/*-----border width------*/
.border-width-1px {
  border-width: 1px !important;
}

.border-width-2px {
  border-width: 2px !important;
}

.border-width-3px {
  border-width: 3px !important;
}

.border-width-4px {
  border-width: 4px !important;
}

.border-width-5px {
  border-width: 5px !important;
}

.border-width-6px {
  border-width: 6px !important;
}

/*-----transform-position------*/
.position-absolute {
  position: absolute;
}

.position-static {
  position: static;
}

.position-fixed {
  position: fixed;
}

.position-inherit {
  position: inherit;
}

.position-initial {
  position: initial;
}

.position-relative {
  position: relative;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
