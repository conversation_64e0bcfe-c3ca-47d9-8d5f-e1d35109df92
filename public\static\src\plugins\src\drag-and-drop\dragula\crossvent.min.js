// crossvent@v1.5.4, MIT licensed. https://github.com/bevacqua/crossvent
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;"undefined"!=typeof window?n=window:"undefined"!=typeof global?n=global:"undefined"!=typeof self&&(n=self),n.crossvent=e()}}(function(){return function e(n,t,r){function o(u,f){if(!t[u]){if(!n[u]){var a="function"==typeof require&&require;if(!f&&a)return a(u,!0);if(i)return i(u,!0);throw new Error("Cannot find module '"+u+"'")}var c=t[u]={exports:{}};n[u][0].call(c.exports,function(e){var t=n[u][1][e];return o(t?t:e)},c,c.exports,e,n,t,r)}return t[u].exports}for(var i="function"==typeof require&&require,u=0;u<r.length;u++)o(r[u]);return o}({1:[function(e,n){(function(e){function t(){try{var e=new r("cat",{detail:{foo:"bar"}});return"cat"===e.type&&"bar"===e.detail.foo}catch(n){}return!1}var r=e.CustomEvent;n.exports=t()?r:"function"==typeof document.createEvent?function(e,n){var t=document.createEvent("CustomEvent");return n?t.initCustomEvent(e,n.bubbles,n.cancelable,n.detail):t.initCustomEvent(e,!1,!1,void 0),t}:function(e,n){var t=document.createEventObject();return t.type=e,n?(t.bubbles=Boolean(n.bubbles),t.cancelable=Boolean(n.cancelable),t.detail=n.detail):(t.bubbles=!1,t.cancelable=!1,t.detail=void 0),t}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(e,n){(function(t){"use strict";function r(e,n,t,r){return e.addEventListener(n,t,r)}function o(e,n,t){return e.attachEvent("on"+n,c(e,n,t))}function i(e,n,t,r){return e.removeEventListener(n,t,r)}function u(e,n,t){var r=d(e,n,t);return r?e.detachEvent("on"+n,r):void 0}function f(e,n,t){function r(){var e;return p.createEvent?(e=p.createEvent("Event"),e.initEvent(n,!0,!0)):p.createEventObject&&(e=p.createEventObject()),e}function o(){return new v(n,{detail:t})}var i=-1===s.indexOf(n)?o():r();e.dispatchEvent?e.dispatchEvent(i):e.fireEvent("on"+n,i)}function a(e,n,r){return function(n){var o=n||t.event;o.target=o.target||o.srcElement,o.preventDefault=o.preventDefault||function(){o.returnValue=!1},o.stopPropagation=o.stopPropagation||function(){o.cancelBubble=!0},o.which=o.which||o.keyCode,r.call(e,o)}}function c(e,n,t){var r=d(e,n,t)||a(e,n,t);return E.push({wrapper:r,element:e,type:n,fn:t}),r}function d(e,n,t){var r=l(e,n,t);if(r){var o=E[r].wrapper;return E.splice(r,1),o}}function l(e,n,t){var r,o;for(r=0;r<E.length;r++)if(o=E[r],o.element===e&&o.type===n&&o.fn===t)return r}var v=e("custom-event"),s=e("./eventmap"),p=t.document,b=r,w=i,E=[];t.addEventListener||(b=o,w=u),n.exports={add:b,remove:w,fabricate:f}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./eventmap":3,"custom-event":1}],3:[function(e,n){(function(e){"use strict";var t=[],r="",o=/^on/;for(r in e)o.test(r)&&t.push(r.slice(2));n.exports=t}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[2])(2)});