/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
/*
==================
    Switches
==================
*/
.switch {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.switch .switch-input {
  float: left;
  margin-left: -1.5em;
}

.switch-input {
  width: 1em;
  height: 1em;
  vertical-align: top;
  background-color: #bfc9d4;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 2px solid #bfc9d4;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-print-color-adjust: exact;
  color-adjust: exact;
  width: 48px;
  height: 25px;
  cursor: pointer;
}
.switch-input[type=checkbox] {
  border-radius: 0.25em;
}
.switch-input[type=radio] {
  border-radius: 50%;
}
.switch-input:active {
  filter: brightness(90%);
}
.switch-input:focus {
  outline: 0;
}
.switch-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.switch-input:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.switch-input:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}
.switch-input[type=checkbox]:indeterminate {
  background-color: #0d6efd;
  border-color: #0d6efd;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.switch-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.switch-input[disabled] ~ .switch-label, .switch-input:disabled ~ .switch-label {
  opacity: 0.5;
}

.form-switch-custom {
  padding-left: 2.5em;
}
.form-switch-custom .switch-input {
  margin-left: -2.5em;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: %231b2e4b;'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm4.207 12.793-1.414 1.414L12 13.414l-2.793 2.793-1.414-1.414L10.586 12 7.793 9.207l1.414-1.414L12 10.586l2.793-2.793 1.414 1.414L13.414 12l2.793 2.793z'%3E%3C/path%3E%3C/svg%3E");
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
.form-switch-custom .switch-input:focus, .form-switch-custom .switch-input:active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: %231b2e4b;'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm4.207 12.793-1.414 1.414L12 13.414l-2.793 2.793-1.414-1.414L10.586 12 7.793 9.207l1.414-1.414L12 10.586l2.793-2.793 1.414 1.414L13.414 12l2.793 2.793z'%3E%3C/path%3E%3C/svg%3E");
}
.form-switch-custom .switch-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm-1.999 14.413-3.713-3.705L7.7 11.292l2.299 2.295 5.294-5.294 1.414 1.414-6.706 6.706z'%3E%3C/path%3E%3C/svg%3E");
}

@media (prefers-reduced-motion: reduce) {
  .form-switch-custom .switch-input {
    transition: none;
  }
}
.switch-inline {
  display: inline-block;
  margin-right: 1rem;
}
.switch-inline .input-checkbox {
  display: block;
  float: left;
  position: relative;
}
.switch-inline label {
  margin-bottom: 0;
  vertical-align: -webkit-baseline-middle;
  vertical-align: -moz-middle-with-baseline;
  margin-left: 8px;
  vertical-align: sub;
  vertical-align: text-top;
  cursor: pointer;
}

.form-switch-custom.form-switch-primary .switch-input:checked {
  background-color: #4361ee;
  border-color: #4361ee;
}
.form-switch-custom.form-switch-info .switch-input:checked {
  background-color: #2196f3;
  border-color: #2196f3;
}
.form-switch-custom.form-switch-success .switch-input:checked {
  background-color: #00ab55;
  border-color: #00ab55;
}
.form-switch-custom.form-switch-warning .switch-input:checked {
  background-color: #e2a03f;
  border-color: #e2a03f;
}
.form-switch-custom.form-switch-secondary .switch-input:checked {
  background-color: #805dca;
  border-color: #805dca;
}
.form-switch-custom.form-switch-danger .switch-input:checked {
  background-color: #e7515a;
  border-color: #e7515a;
}
.form-switch-custom.form-switch-dark .switch-input:checked {
  background-color: #3b3f5c;
  border-color: #3b3f5c;
}

/* 
====================
    SLIM TOGGLE
====================
*/
.switch-inline.slim-toggle .input-checkbox {
  display: block;
  float: left;
  position: relative;
}
.switch-inline.slim-toggle .input-checkbox:before {
  position: absolute;
  content: "";
  left: -31px;
  right: 0;
  background: #e7515a;
  width: 193%;
  height: 5px;
  top: 42.5%;
  border-radius: 60px;
  width: 42px;
  z-index: 0;
}

.slim-toggle.form-switch-custom .switch-input {
  background-color: transparent !important;
  border: none !important;
  z-index: 2;
  position: relative;
}
.slim-toggle.form-switch-custom .switch-input:checked {
  background-color: transparent !important;
  border: none !important;
  z-index: 2;
  position: relative;
}

.switch-inline.slim-toggle .switch-input {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgb(191 201 212);'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2z'%3E%3C/path%3E%3C/svg%3E");
}

/* 
========================
    MATERIAL TOGGLE
========================
*/
.material-toggle .switch-input {
  height: 23px;
}

.switch-inline.material-toggle .input-checkbox {
  display: block;
  float: left;
  position: relative;
}
.switch-inline.material-toggle .input-checkbox:before {
  position: absolute;
  content: "";
  left: -29px;
  right: 0;
  background: #e7515a;
  width: 193%;
  height: 14px;
  top: 22.5%;
  border-radius: 60px;
  width: 36px;
  z-index: 0;
}

.material-toggle.form-switch-custom .switch-input {
  background-color: transparent !important;
  border: none !important;
  z-index: 2;
  position: relative;
}
.material-toggle.form-switch-custom .switch-input:checked {
  background-color: transparent !important;
  border: none !important;
  z-index: 2;
  position: relative;
}

.switch-inline.material-toggle .switch-input {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgb(191 201 212);'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2z'%3E%3C/path%3E%3C/svg%3E");
}
.switch-inline.inner-text-toggle .input-checkbox {
  display: block;
  float: left;
  position: relative;
}
.switch-inline.inner-text-toggle .input-checkbox span.switch-chk-label {
  position: absolute;
  font-size: 8px;
  top: 7px;
  color: #fff;
  pointer-events: none;
}
.switch-inline.inner-text-toggle .input-checkbox span.label-left {
  left: -30px;
  z-index: 3;
}
.switch-inline.inner-text-toggle .input-checkbox span.label-right {
  left: -7px;
  z-index: 3;
}

/* 
========================
    Inner Text
========================
*/
.inner-text-toggle.form-switch-custom .switch-input {
  z-index: 2;
  position: relative;
}
.inner-text-toggle.form-switch-custom .switch-input:checked {
  z-index: 2;
  position: relative;
}

.switch-inline.inner-text-toggle .switch-input {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2z'%3E%3C/path%3E%3C/svg%3E");
}

.inner-text-toggle.form-switch-custom .switch-input:active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2z'%3E%3C/path%3E%3C/svg%3E");
  filter: none;
}

/* 
========================
    Inner Icon
========================
*/
.switch-inline.inner-icon-toggle .input-checkbox {
  display: block;
  float: left;
  position: relative;
}
.switch-inline.inner-icon-toggle .input-checkbox span.switch-chk-label {
  position: absolute;
  font-size: 8px;
  top: 5.5px;
  color: #fff;
  pointer-events: none;
}
.switch-inline.inner-icon-toggle .input-checkbox span.label-left {
  left: -30px;
  z-index: 3;
}
.switch-inline.inner-icon-toggle .input-checkbox span.label-right {
  left: -7px;
  z-index: 3;
}
.switch-inline.inner-icon-toggle .input-checkbox span.switch-chk-label svg {
  width: 15px;
  height: 15px;
  fill: #fff;
}

.inner-icon-toggle.form-switch-custom .switch-input {
  z-index: 2;
  position: relative;
}
.inner-icon-toggle.form-switch-custom .switch-input:checked {
  z-index: 2;
  position: relative;
}

.switch-inline.inner-icon-toggle .switch-input {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2z'%3E%3C/path%3E%3C/svg%3E");
  background-color: #000;
  border-color: #515365;
}
.switch-inline.inner-icon-toggle .switch-input:checked {
  background-color: #e2a03f;
  border-color: #e2a03f;
}

.inner-icon-toggle.form-switch-custom .switch-input:active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);'%3E%3Cpath d='M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2z'%3E%3C/path%3E%3C/svg%3E");
  filter: none;
}

/* 
========================
    Inner Icon Circle
========================
*/
.inner-icon-circle-toggle {
  padding: 0;
}
.inner-icon-circle-toggle .switch-label {
  vertical-align: sub;
}
.inner-icon-circle-toggle .switch-input {
  width: 30px;
  height: 30px;
  margin-right: 0;
  margin-left: 0;
  background-size: 21px;
  background-position: center;
}

.switch-inline.inner-icon-circle-toggle .input-checkbox {
  display: block;
  float: left;
  position: relative;
}

.inner-icon-circle-toggle.form-switch-custom .switch-input {
  z-index: 2;
  position: relative;
}
.inner-icon-circle-toggle.form-switch-custom .switch-input:checked {
  z-index: 2;
  position: relative;
}

.switch-inline.inner-icon-circle-toggle .switch-input {
  background-color: #515365;
  border-color: #515365;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);transform: ;msFilter:;'%3E%3Cpath d='M12 11.807A9.002 9.002 0 0 1 10.049 2a9.942 9.942 0 0 0-5.12 2.735c-3.905 3.905-3.905 10.237 0 14.142 3.906 3.906 10.237 3.905 14.143 0a9.946 9.946 0 0 0 2.735-5.119A9.003 9.003 0 0 1 12 11.807z'%3E%3C/path%3E%3C/svg%3E");
}
.switch-inline.inner-icon-circle-toggle .switch-input:checked {
  background-color: #805dca;
  border-color: #805dca;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);'%3E%3Cpath d='M6.995 12c0 2.761 2.246 5.007 5.007 5.007s5.007-2.246 5.007-5.007-2.246-5.007-5.007-5.007S6.995 9.239 6.995 12zM11 19h2v3h-2zm0-17h2v3h-2zm-9 9h3v2H2zm17 0h3v2h-3zM5.637 19.778l-1.414-1.414 2.121-2.121 1.414 1.414zM16.242 6.344l2.122-2.122 1.414 1.414-2.122 2.122zM6.344 7.759 4.223 5.637l1.415-1.414 2.12 2.122zm13.434 10.605-1.414 1.414-2.122-2.122 1.414-1.414z'%3E%3C/path%3E%3C/svg%3E");
  background-position: center;
}

.inner-icon-circle-toggle.form-switch-custom .switch-input:active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);transform: ;msFilter:;'%3E%3Cpath d='M12 11.807A9.002 9.002 0 0 1 10.049 2a9.942 9.942 0 0 0-5.12 2.735c-3.905 3.905-3.905 10.237 0 14.142 3.906 3.906 10.237 3.905 14.143 0a9.946 9.946 0 0 0 2.735-5.119A9.003 9.003 0 0 1 12 11.807z'%3E%3C/path%3E%3C/svg%3E");
  filter: none;
}

.switch-inline.inner-icon-circle-toggle .switch-input:checked:active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' style='fill: rgba(255, 255, 255, 1);'%3E%3Cpath d='M6.995 12c0 2.761 2.246 5.007 5.007 5.007s5.007-2.246 5.007-5.007-2.246-5.007-5.007-5.007S6.995 9.239 6.995 12zM11 19h2v3h-2zm0-17h2v3h-2zm-9 9h3v2H2zm17 0h3v2h-3zM5.637 19.778l-1.414-1.414 2.121-2.121 1.414 1.414zM16.242 6.344l2.122-2.122 1.414 1.414-2.122 2.122zM6.344 7.759 4.223 5.637l1.415-1.414 2.12 2.122zm13.434 10.605-1.414 1.414-2.122-2.122 1.414-1.414z'%3E%3C/path%3E%3C/svg%3E");
}

/* 
========================
    Dual Label Circle
========================
*/
.dual-label-toggle {
  padding: 0;
  display: inline-flex;
  margin: 0;
}
.dual-label-toggle .switch-label {
  align-self: center;
  margin: 0;
}
.dual-label-toggle .switch-label-left {
  margin-right: 8px;
}
.dual-label-toggle .switch-label-right {
  margin-left: 8px;
}
.dual-label-toggle .input-checkbox {
  float: none;
}
.dual-label-toggle .switch-input {
  float: none;
  margin: 0;
}

/* 
========================
    Inner Label
========================
*/
.switch.inner-label-toggle {
  padding: 0;
  margin-right: 0;
  margin-bottom: 0;
  overflow: hidden;
}
.switch.inner-label-toggle .switch-input {
  min-width: 150px;
  height: 44px;
  border-radius: 8px;
  margin-left: 0;
}

.switch-inline.inner-label-toggle .input-checkbox {
  display: block;
  float: left;
  position: relative;
}
.switch-inline.inner-label-toggle .input-checkbox:before {
  content: "";
  position: absolute;
  height: 90%;
  width: 50%;
  background: #fff;
  top: 2px;
  z-index: 3;
  left: 2px;
  border-radius: 8px;
  transition: 0.5s;
  pointer-events: none;
}
.switch-inline.inner-label-toggle.show .input-checkbox:before {
  left: 73px;
}
.switch-inline.inner-label-toggle.show .input-checkbox span.label-left {
  color: #fff;
}
.switch-inline.inner-label-toggle.show .input-checkbox span.label-left svg {
  fill: #fff;
}
.switch-inline.inner-label-toggle.show .input-checkbox span.label-right {
  color: #000;
}
.switch-inline.inner-label-toggle.show .input-checkbox span.label-right svg {
  fill: #000;
}
.switch-inline.inner-label-toggle.show .input-checkbox:before {
  background: #e0e6ed;
}
.switch-inline.inner-label-toggle .input-checkbox span.switch-chk-label {
  position: absolute;
  font-size: 17px;
  top: 10px;
  color: #000;
  pointer-events: none;
  border-radius: 8px !important;
  font-size: 14px;
  width: 50%;
  display: block;
  text-align: center;
}
.switch-inline.inner-label-toggle .input-checkbox span.switch-chk-label svg {
  fill: #000;
  width: 17px;
  height: 17px;
  vertical-align: sub;
}
.switch-inline.inner-label-toggle .input-checkbox span.label-left {
  z-index: 3;
  top: 28%;
  color: #000;
}
.switch-inline.inner-label-toggle .input-checkbox span.label-left svg {
  fill: #000;
}
.switch-inline.inner-label-toggle .input-checkbox span.label-right {
  right: 0;
  z-index: 3;
  top: 28%;
}

.inner-label-toggle.form-switch-custom .switch-input {
  z-index: 2;
  position: relative;
}
.inner-label-toggle.form-switch-custom .switch-input:checked {
  z-index: 2;
  position: relative;
}

.switch-inline.inner-label-toggle .switch-input {
  background-image: none;
}

.inner-label-toggle.form-switch-custom .switch-input:active {
  background-image: none;
  filter: none;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
