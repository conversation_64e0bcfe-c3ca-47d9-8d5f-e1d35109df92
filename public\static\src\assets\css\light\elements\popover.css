/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.popovers-section h6 {
  color: #3b3f5c;
  font-size: 0.875rem;
  margin-top: 25px;
  margin-bottom: 20px;
}

.popover {
  background-color: #fff;
  border: 1px solid #e0e6ed;
  border-radius: 6px;
}
.popover .popover-header {
  border-radius: 0;
  color: #060818;
}
.popover .popover-body {
  color: #060818;
  padding: 0.5rem 0.75rem;
}

/*
	Popovers
*/
.popover-primary, .popover-success, .popover-info, .popover-danger, .popover-warning, .popover-secondary, .popover-dark {
  border-color: #fff;
}

/* 		popover Arrow 	*/
.bs-popover-top.popover .popover-arrow:before {
  border-top-color: #e0e6ed;
}

.bs-popover-bottom.popover .popover-arrow:before {
  border-bottom-color: #e0e6ed;
}

.bs-popover-end.popover .popover-arrow:before {
  border-right-color: #e0e6ed;
}

.bs-popover-start.popover .popover-arrow:before {
  border-left-color: #e0e6ed;
}

.popover-primary .popover-arrow:after {
  border-top-color: #eceffe;
}

.popover-success .popover-arrow:after {
  border-top-color: #ddf5f0;
}

.popover-info .popover-arrow:after {
  border-top-color: #e6f4ff;
}

.popover-danger .popover-arrow:after {
  border-top-color: #fbeced;
}

.popover-warning .popover-arrow:after {
  border-top-color: #fcf5e9;
}

.popover-secondary .popover-arrow:after {
  border-top-color: #f2eafa;
}

.popover-dark .popover-arrow:after {
  border-top-color: #eaeaec;
}

.popover-primary,
.popover-success,
.popover-info,
.popover-danger,
.popover-warning,
.popover-secondary,
.popover-dark {
  border: none;
}

/* 		popover Header 		*/
.popover-primary .popover-header {
  background-color: #eceffe;
  border: none;
  color: #4361ee;
}

.popover-success .popover-header {
  background-color: #ddf5f0;
  border: none;
  color: #00ab55;
}

.popover-info .popover-header {
  background-color: #e6f4ff;
  border: none;
  color: #2196f3;
}

.popover-danger .popover-header {
  background-color: #fbeced;
  border: none;
  color: #e7515a;
}

.popover-warning .popover-header {
  background-color: #fcf5e9;
  border: none;
  color: #e2a03f;
}

.popover-secondary .popover-header {
  background-color: #f2eafa;
  border: none;
  color: #805dca;
}

.popover-dark .popover-header {
  background-color: #eaeaec;
  border: none;
  color: #3b3f5c;
}

/*  	Popover Body 	*/
.popover-primary .popover-body {
  background-color: #eceffe;
  color: #4361ee;
}

.popover-success .popover-body {
  background-color: #ddf5f0;
  color: #00ab55;
}

.popover-info .popover-body {
  background-color: #e6f4ff;
  color: #2196f3;
}

.popover-danger .popover-body {
  background-color: #fbeced;
  color: #e7515a;
}

.popover-warning .popover-body {
  background-color: #fcf5e9;
  color: #e2a03f;
}

.popover-secondary .popover-body {
  background-color: #f2eafa;
  color: #805dca;
}

.popover-dark .popover-body {
  background-color: #eaeaec;
  color: #3b3f5c;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4uL2Jhc2UvX2Z1bmN0aW9ucy5zY3NzIiwiLi4vYmFzZS9fbWl4aW5zLnNjc3MiLCJlbGVtZW50cy9wb3BvdmVyLnNjc3MiLCIuLi9iYXNlL19jb2xvcl92YXJpYWJsZXMuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FDQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQ0NBO0VBQ0UsT0NhSztFRFpMO0VBQ0E7RUFDQTs7O0FBR0Y7RUFDRTtFQUNBO0VBQ0E7O0FBRUE7RUFDRTtFQUNBOztBQUdGO0VBQ0U7RUFDQTs7O0FBSUo7QUFBQTtBQUFBO0FBSUE7RUFDRTs7O0FBR0Y7QUFLRTtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLRjtFQUNFOzs7QUFLSjtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtFQU9FOzs7QUFHRjtBQUVBO0VBQ0U7RUFDQTtFQUNBLE9DN0dROzs7QURnSFY7RUFDRTtFQUNBO0VBQ0E7OztBQUdGO0VBQ0U7RUFDQTtFQUNBLE9DeEhLOzs7QUQySFA7RUFDRTtFQUNBO0VBQ0EsT0MzSE87OztBRDhIVDtFQUNFO0VBQ0E7RUFDQSxPQ2xJUTs7O0FEcUlWO0VBQ0U7RUFDQTtFQUNBLE9DdElVOzs7QUR5SVo7RUFDRTtFQUNBO0VBQ0EsT0MzSUs7OztBRDhJUDtBQUVBO0VBQ0U7RUFDQSxPQ3hKUTs7O0FEMkpWO0VBQ0U7RUFDQTs7O0FBR0Y7RUFDRTtFQUNBLE9DaktLOzs7QURvS1A7RUFDRTtFQUNBLE9DbktPOzs7QURzS1Q7RUFDRTtFQUNBLE9DektROzs7QUQ0S1Y7RUFDRTtFQUNBLE9DNUtVOzs7QUQrS1o7RUFDRTtFQUNBLE9DaExLIiwiZmlsZSI6ImVsZW1lbnRzL3BvcG92ZXIuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLypcclxuXHQ9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XHJcblx0XHRcdEBJbXBvcnRcdEZ1bmN0aW9uXHJcblx0PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxyXG4qL1xyXG5cclxuIiwiLypcclxuXHQ9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XHJcblx0XHRcdEBJbXBvcnRcdE1peGluc1xyXG5cdD09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cclxuKi9cclxuXHJcbi8vIEJvcmRlclxyXG4kZGlyZWN0aW9uOiAnJztcclxuQG1peGluIGJvcmRlcigkZGlyZWN0aW9uLCAkd2lkdGgsICRzdHlsZSwgJGNvbG9yKSB7XHJcblxyXG4gICBAaWYgJGRpcmVjdGlvbiA9PSAnJyB7XHJcbiAgICAgICAgYm9yZGVyOiAkd2lkdGggJHN0eWxlICRjb2xvcjtcclxuICAgfSBAZWxzZSB7XHJcbiAgICAgICAgYm9yZGVyLSN7JGRpcmVjdGlvbn06ICR3aWR0aCAkc3R5bGUgJGNvbG9yO1xyXG4gICB9XHJcbn0iLCJAaW1wb3J0ICcuLi8uLi9iYXNlL2Jhc2UnO1xyXG4ucG9wb3ZlcnMtc2VjdGlvbiBoNiB7XHJcbiAgY29sb3I6ICRkYXJrO1xyXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgbWFyZ2luLXRvcDogMjVweDtcclxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG59XHJcblxyXG4ucG9wb3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZTBlNmVkO1xyXG4gIGJvcmRlci1yYWRpdXM6IDZweDtcclxuXHJcbiAgLnBvcG92ZXItaGVhZGVyIHtcclxuICAgIGJvcmRlci1yYWRpdXM6IDA7XHJcbiAgICBjb2xvcjogIzA2MDgxODtcclxuICB9XHJcblxyXG4gIC5wb3BvdmVyLWJvZHkge1xyXG4gICAgY29sb3I6ICMwNjA4MTg7XHJcbiAgICBwYWRkaW5nOiAuNXJlbSAuNzVyZW07XHJcbiAgfVxyXG59XHJcblxyXG4vKlxyXG5cdFBvcG92ZXJzXHJcbiovXHJcblxyXG4ucG9wb3Zlci1wcmltYXJ5LCAucG9wb3Zlci1zdWNjZXNzLCAucG9wb3Zlci1pbmZvLCAucG9wb3Zlci1kYW5nZXIsIC5wb3BvdmVyLXdhcm5pbmcsIC5wb3BvdmVyLXNlY29uZGFyeSwgLnBvcG92ZXItZGFyayB7XHJcbiAgYm9yZGVyLWNvbG9yOiAjZmZmO1xyXG59XHJcblxyXG4vKiBcdFx0cG9wb3ZlciBBcnJvdyBcdCovXHJcblxyXG5cclxuXHJcbi5icy1wb3BvdmVyLXRvcC5wb3BvdmVyIC5wb3BvdmVyLWFycm93IHtcclxuICAmOmJlZm9yZSB7XHJcbiAgICBib3JkZXItdG9wLWNvbG9yOiAjZTBlNmVkO1xyXG4gIH1cclxufVxyXG5cclxuLmJzLXBvcG92ZXItYm90dG9tLnBvcG92ZXIgLnBvcG92ZXItYXJyb3cge1xyXG4gICY6YmVmb3JlIHtcclxuICAgIGJvcmRlci1ib3R0b20tY29sb3I6ICNlMGU2ZWQ7XHJcbiAgfVxyXG59XHJcblxyXG4uYnMtcG9wb3Zlci1lbmQucG9wb3ZlciAucG9wb3Zlci1hcnJvdyB7XHJcbiAgJjpiZWZvcmUge1xyXG4gICAgYm9yZGVyLXJpZ2h0LWNvbG9yOiAjZTBlNmVkO1xyXG4gIH1cclxufVxyXG5cclxuLmJzLXBvcG92ZXItc3RhcnQucG9wb3ZlciAucG9wb3Zlci1hcnJvdyB7XHJcbiAgJjpiZWZvcmUge1xyXG4gICAgYm9yZGVyLWxlZnQtY29sb3I6ICNlMGU2ZWQ7XHJcbiAgfVxyXG59XHJcblxyXG4ucG9wb3Zlci1wcmltYXJ5IC5wb3BvdmVyLWFycm93IHtcclxuICAmOmFmdGVyIHtcclxuICAgIGJvcmRlci10b3AtY29sb3I6ICNlY2VmZmU7XHJcbiAgfVxyXG59XHJcblxyXG4ucG9wb3Zlci1zdWNjZXNzIC5wb3BvdmVyLWFycm93IHtcclxuICAmOmFmdGVyIHtcclxuICAgIGJvcmRlci10b3AtY29sb3I6ICNkZGY1ZjA7XHJcbiAgfVxyXG59XHJcblxyXG4ucG9wb3Zlci1pbmZvIC5wb3BvdmVyLWFycm93IHtcclxuICAmOmFmdGVyIHtcclxuICAgIGJvcmRlci10b3AtY29sb3I6ICNlNmY0ZmY7XHJcbiAgfVxyXG59XHJcblxyXG4ucG9wb3Zlci1kYW5nZXIgLnBvcG92ZXItYXJyb3cge1xyXG4gICY6YWZ0ZXIge1xyXG4gICAgYm9yZGVyLXRvcC1jb2xvcjogI2ZiZWNlZDtcclxuICB9XHJcbn1cclxuXHJcbi5wb3BvdmVyLXdhcm5pbmcgLnBvcG92ZXItYXJyb3cge1xyXG4gICY6YWZ0ZXIge1xyXG4gICAgYm9yZGVyLXRvcC1jb2xvcjogI2ZjZjVlOTtcclxuICB9XHJcbn1cclxuXHJcbi5wb3BvdmVyLXNlY29uZGFyeSAucG9wb3Zlci1hcnJvdyB7XHJcbiAgJjphZnRlciB7XHJcbiAgICBib3JkZXItdG9wLWNvbG9yOiAjZjJlYWZhO1xyXG4gIH1cclxufVxyXG5cclxuLnBvcG92ZXItZGFyayAucG9wb3Zlci1hcnJvdyB7XHJcbiAgJjphZnRlciB7XHJcbiAgICBib3JkZXItdG9wLWNvbG9yOiAjZWFlYWVjO1xyXG4gIH1cclxufVxyXG5cclxuXHJcbi5wb3BvdmVyLXByaW1hcnksXHJcbi5wb3BvdmVyLXN1Y2Nlc3MsXHJcbi5wb3BvdmVyLWluZm8sXHJcbi5wb3BvdmVyLWRhbmdlcixcclxuLnBvcG92ZXItd2FybmluZyxcclxuLnBvcG92ZXItc2Vjb25kYXJ5LFxyXG4ucG9wb3Zlci1kYXJrIHtcclxuICBib3JkZXI6IG5vbmU7XHJcbn1cclxuXHJcbi8qIFx0XHRwb3BvdmVyIEhlYWRlciBcdFx0Ki9cclxuXHJcbi5wb3BvdmVyLXByaW1hcnkgLnBvcG92ZXItaGVhZGVyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNlZmZlO1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBjb2xvcjogJHByaW1hcnk7XHJcbn1cclxuXHJcbi5wb3BvdmVyLXN1Y2Nlc3MgLnBvcG92ZXItaGVhZGVyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGRmNWYwO1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBjb2xvcjogIzAwYWI1NTtcclxufVxyXG5cclxuLnBvcG92ZXItaW5mbyAucG9wb3Zlci1oZWFkZXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlNmY0ZmY7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIGNvbG9yOiAkaW5mbztcclxufVxyXG5cclxuLnBvcG92ZXItZGFuZ2VyIC5wb3BvdmVyLWhlYWRlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZiZWNlZDtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgY29sb3I6ICRkYW5nZXI7XHJcbn1cclxuXHJcbi5wb3BvdmVyLXdhcm5pbmcgLnBvcG92ZXItaGVhZGVyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmNmNWU5O1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBjb2xvcjogJHdhcm5pbmc7XHJcbn1cclxuXHJcbi5wb3BvdmVyLXNlY29uZGFyeSAucG9wb3Zlci1oZWFkZXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmMmVhZmE7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIGNvbG9yOiAkc2Vjb25kYXJ5O1xyXG59XHJcblxyXG4ucG9wb3Zlci1kYXJrIC5wb3BvdmVyLWhlYWRlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2VhZWFlYztcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgY29sb3I6ICRkYXJrO1xyXG59XHJcblxyXG4vKiAgXHRQb3BvdmVyIEJvZHkgXHQqL1xyXG5cclxuLnBvcG92ZXItcHJpbWFyeSAucG9wb3Zlci1ib2R5IHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNlZmZlO1xyXG4gIGNvbG9yOiAkcHJpbWFyeTtcclxufVxyXG5cclxuLnBvcG92ZXItc3VjY2VzcyAucG9wb3Zlci1ib2R5IHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGRmNWYwO1xyXG4gIGNvbG9yOiAjMDBhYjU1O1xyXG59XHJcblxyXG4ucG9wb3Zlci1pbmZvIC5wb3BvdmVyLWJvZHkge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlNmY0ZmY7XHJcbiAgY29sb3I6ICRpbmZvO1xyXG59XHJcblxyXG4ucG9wb3Zlci1kYW5nZXIgLnBvcG92ZXItYm9keSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZiZWNlZDtcclxuICBjb2xvcjogJGRhbmdlcjtcclxufVxyXG5cclxuLnBvcG92ZXItd2FybmluZyAucG9wb3Zlci1ib2R5IHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmNmNWU5O1xyXG4gIGNvbG9yOiAkd2FybmluZztcclxufVxyXG5cclxuLnBvcG92ZXItc2Vjb25kYXJ5IC5wb3BvdmVyLWJvZHkge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmMmVhZmE7XHJcbiAgY29sb3I6ICRzZWNvbmRhcnk7XHJcbn1cclxuXHJcbi5wb3BvdmVyLWRhcmsgLnBvcG92ZXItYm9keSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2VhZWFlYztcclxuICBjb2xvcjogJGRhcms7XHJcbn1cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuLy8gLnBvcG92ZXItcHJpbWFyeSAucG9wb3Zlci1ib2R5IHtcclxuLy8gICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNlZmZlO1xyXG4vLyAgIGNvbG9yOiAkcHJpbWFyeTtcclxuLy8gfVxyXG5cclxuLy8gLnBvcG92ZXItc3VjY2VzcyAucG9wb3Zlci1ib2R5IHtcclxuLy8gICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGRmNWYwO1xyXG4vLyAgIGNvbG9yOiAjMDBhYjU1O1xyXG4vLyB9XHJcblxyXG4vLyAucG9wb3Zlci1pbmZvIC5wb3BvdmVyLWJvZHkge1xyXG4vLyAgIGJhY2tncm91bmQtY29sb3I6ICNlNmY0ZmY7XHJcbi8vICAgY29sb3I6ICRpbmZvO1xyXG4vLyB9XHJcblxyXG4vLyAucG9wb3Zlci1kYW5nZXIgLnBvcG92ZXItYm9keSB7XHJcbi8vICAgYmFja2dyb3VuZC1jb2xvcjogI2ZiZWNlZDtcclxuLy8gICBjb2xvcjogJGRhbmdlcjtcclxuLy8gfVxyXG5cclxuLy8gLnBvcG92ZXItd2FybmluZyAucG9wb3Zlci1ib2R5IHtcclxuLy8gICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmNmNWU5O1xyXG4vLyAgIGNvbG9yOiAkd2FybmluZztcclxuLy8gfVxyXG5cclxuLy8gLnBvcG92ZXItc2Vjb25kYXJ5IC5wb3BvdmVyLWJvZHkge1xyXG4vLyAgIGJhY2tncm91bmQtY29sb3I6ICNmMmVhZmE7XHJcbi8vICAgY29sb3I6ICRzZWNvbmRhcnk7XHJcbi8vIH1cclxuXHJcbi8vIC5wb3BvdmVyLWRhcmsgLnBvcG92ZXItYm9keSB7XHJcbi8vICAgYmFja2dyb3VuZC1jb2xvcjogI2VhZWFlYztcclxuLy8gICBjb2xvcjogJGRhcms7XHJcbi8vIH0iLCJcclxuLy9cdD09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cclxuLy9cdFx0XHRASW1wb3J0XHRDb2xvcnNcclxuLy9cdD09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cclxuXHJcblxyXG4kd2hpdGU6ICNmZmY7XHJcbiRibGFjazogIzAwMDtcclxuXHJcbiRwcmltYXJ5OiAjNDM2MWVlO1xyXG4kaW5mbzogIzIxOTZmMztcclxuJHN1Y2Nlc3M6ICMwMGFiNTU7XHJcbiR3YXJuaW5nOiAjZTJhMDNmO1xyXG4kZGFuZ2VyOiAjZTc1MTVhO1xyXG4kc2Vjb25kYXJ5OiAjODA1ZGNhO1xyXG4kZGFyazogIzNiM2Y1YztcclxuXHJcblxyXG4kbC1wcmltYXJ5OiAjZWNlZmZlO1xyXG4kbC1pbmZvOiAjZTZmNGZmO1xyXG4kbC1zdWNjZXNzOiAjZGRmNWYwO1xyXG4kbC13YXJuaW5nOiAjZmNmNWU5O1xyXG4kbC1kYW5nZXI6ICNmYmVjZWQ7XHJcbiRsLXNlY29uZGFyeTogI2YyZWFmYTtcclxuJGwtZGFyazogI2VhZWFlYztcclxuXHJcbi8vIFx0PT09PT09PT09PT09PT09PT1cclxuLy9cdFx0TW9yZSBDb2xvcnNcclxuLy9cdD09PT09PT09PT09PT09PT09XHJcblxyXG4kbS1jb2xvcl8wOiAjZmFmYWZhO1xyXG4kbS1jb2xvcl8xOiAjZjFmMmYzO1xyXG4kbS1jb2xvcl8yOiAjZWJlZGYyO1xyXG5cclxuJG0tY29sb3JfMzogI2UwZTZlZDtcclxuJG0tY29sb3JfNDogI2JmYzlkNDtcclxuJG0tY29sb3JfNTogI2QzZDNkMztcclxuXHJcbiRtLWNvbG9yXzY6ICM4ODhlYTg7XHJcbiRtLWNvbG9yXzc6ICM1MDY2OTA7XHJcblxyXG4kbS1jb2xvcl84OiAjNTU1NTU1O1xyXG4kbS1jb2xvcl85OiAjNTE1MzY1O1xyXG4kbS1jb2xvcl8xMTogIzYwN2Q4YjtcclxuXHJcbiRtLWNvbG9yXzEyOiAjMWIyZTRiO1xyXG4kbS1jb2xvcl8xODogIzE5MWUzYTtcclxuJG0tY29sb3JfMTA6ICMwZTE3MjY7XHJcblxyXG4kbS1jb2xvcl8xOTogIzA2MDgxODtcclxuJG0tY29sb3JfMTM6ICMyMmM3ZDU7XHJcbiRtLWNvbG9yXzE0OiAjMDA5Njg4O1xyXG5cclxuJG0tY29sb3JfMTU6ICNmZmJiNDQ7XHJcbiRtLWNvbG9yXzE2OiAjZTk1ZjJiO1xyXG4kbS1jb2xvcl8xNzogI2Y4NTM4ZDtcclxuXHJcbiRtLWNvbG9yXzIwOiAjNDQ1ZWRlO1xyXG4kbS1jb2xvcl8yMTogIzMwNGFjYTtcclxuXHJcblxyXG4kbS1jb2xvcl8yMjogIzAzMDMwNTtcclxuJG0tY29sb3JfMjM6ICMxNTE1MTY7XHJcbiRtLWNvbG9yXzI0OiAjNjFiNmNkO1xyXG4kbS1jb2xvcl8yNTogIzRjZDI2NTtcclxuXHJcbiRtLWNvbG9yXzI2OiAjN2QzMGNiO1xyXG4kbS1jb2xvcl8yNzogIzAwOGVmZjtcclxuXHJcblxyXG5cclxuXHJcbi8vXHQ9PT09PT09PT09PT09PT09PT09PT09PT1cclxuLy9cdFx0Q29sb3IgRGVmaW5hdGlvblxyXG4vL1x0PT09PT09PT09PT09PT09PT09PT09PT09XHJcblxyXG5cclxuJGJvZHktY29sb3I6ICRtLWNvbG9yXzE5OyJdfQ== */
