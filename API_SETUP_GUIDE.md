# Uroom API Setup Guide

## Prerequisites

- PHP 8.1 or higher
- Laravel 10.x
- MySQL database
- Composer

## Installation Steps

### 1. Database Setup

First, make sure your database is properly configured in your `.env` file:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 2. Run Migrations

Execute the following commands to set up the database tables:

```bash
# Run existing migrations
php artisan migrate

# If you need to create the database first
php artisan migrate:install
php artisan migrate
```

### 3. Seed Default Data (Optional)

Create some sample data for testing:

```bash
# Create a seeder for roles if not exists
php artisan make:seeder RoleSeeder

# Run seeders
php artisan db:seed
```

### 4. Configure Laravel Sanctum

Sanctum should already be configured, but ensure these settings are in place:

In `config/sanctum.php`:
```php
'expiration' => null, // Tokens don't expire by default
```

### 5. Test the API

Start your Laravel development server:

```bash
php artisan serve
```

Your API will be available at: `http://localhost:8000/api/v1`

## Quick Start Testing

### 1. Register a User

```bash
curl -X POST http://localhost:8000/api/v1/register \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "Test",
    "last_name": "User",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "gender": "male"
  }'
```

### 2. Login and Get Token

```bash
curl -X POST http://localhost:8000/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

Save the token from the response for subsequent requests.

### 3. Create an API Key

```bash
curl -X POST http://localhost:8000/api/v1/api-keys \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Integration",
    "permissions": ["rooms:read", "bookings:read", "bookings:write"],
    "rate_limit": 1000
  }'
```

Save the API key and secret from the response.

### 4. Test Room Listing

```bash
# Public access
curl http://localhost:8000/api/v1/rooms

# With API key
curl http://localhost:8000/api/v1/external/rooms \
  -H "X-API-Key: YOUR_API_KEY" \
  -H "X-API-Secret: YOUR_API_SECRET"
```

### 5. Create a Booking

```bash
curl -X POST http://localhost:8000/api/v1/bookings \
  -H "Content-Type: application/json" \
  -d '{
    "room_id": 1,
    "check_in_date": "2024-02-01",
    "duration_months": 6,
    "guest_first_name": "Jane",
    "guest_last_name": "Smith",
    "guest_email": "<EMAIL>",
    "guest_phone": "+1234567890",
    "guest_gender": "female"
  }'
```

## Using Postman

1. Import the `postman_collection.json` file into Postman
2. Set up environment variables:
   - `base_url`: `http://localhost:8000/api/v1`
   - `auth_token`: (will be set automatically after login)
   - `api_key`: (will be set automatically after creating API key)
   - `api_secret`: (will be set automatically after creating API key)

3. Run the requests in order:
   - Register User
   - Login (this will set the auth_token)
   - Create API Key (this will set api_key and api_secret)
   - Test other endpoints

## API Permissions

When creating API keys, use these permission strings:

- `rooms:read` - Read access to rooms
- `rooms:write` - Create/update rooms (admin only)
- `bookings:read` - Read access to bookings
- `bookings:write` - Create/update bookings
- `admin` - Full administrative access
- `*` - All permissions

## Rate Limiting

- Default rate limit for API keys: 1000 requests/hour
- Default rate limit for IP-based requests: 100 requests/hour
- Rate limits are enforced per hour window
- Rate limit headers are included in all responses

## Security Features

- API key authentication with optional secrets
- IP address restrictions (configurable per API key)
- Rate limiting per API key
- CORS headers for web applications
- Security headers (XSS protection, content type options, etc.)
- Request validation and sanitization

## Troubleshooting

### Common Issues

1. **Database connection errors**
   - Check your `.env` database configuration
   - Ensure MySQL is running
   - Verify database exists and user has permissions

2. **Migration errors**
   - Run `php artisan migrate:status` to check migration status
   - Use `php artisan migrate:fresh` to reset and re-run all migrations (WARNING: This will delete all data)

3. **Token authentication not working**
   - Ensure you're including the `Authorization: Bearer {token}` header
   - Check that the token hasn't expired
   - Verify the user account is active (State = 1)

4. **API key authentication not working**
   - Check that you're including both `X-API-Key` and `X-API-Secret` headers
   - Verify the API key is active and not expired
   - Check that your IP address is allowed (if IP restrictions are set)

5. **Rate limit exceeded**
   - Wait for the rate limit window to reset
   - Check the `X-RateLimit-Reset` header for reset time
   - Consider requesting a higher rate limit for your API key

### Debug Mode

For development, ensure debug mode is enabled in `.env`:

```env
APP_DEBUG=true
APP_ENV=local
```

This will provide detailed error messages in API responses.

## Production Considerations

Before deploying to production:

1. Set `APP_DEBUG=false` in your `.env` file
2. Configure proper database credentials
3. Set up SSL/HTTPS for secure API communication
4. Configure proper CORS settings for your frontend domains
5. Set up monitoring and logging for API usage
6. Consider implementing additional security measures like request signing
7. Set up proper backup procedures for your database

## Support

For technical support or questions about the API implementation, please refer to the source code or contact the development team.
