# Uroom API Documentation

## Overview

The Uroom API provides access to room management and booking functionality. The API supports both user authentication via Sanctum tokens and external integration via API keys.

**Base URL:** `http://your-domain.com/api/v1`

## Authentication

### 1. User Authentication (Sanctum Tokens)
For user-facing applications, use Sanctum token authentication.

### 2. API Key Authentication
For external integrations, use API key authentication with the following headers:
- `X-API-Key`: Your API key
- `X-API-Secret`: Your API secret (optional but recommended)

## Rate Limiting

- **API Key requests**: Based on the rate limit configured for your API key (default: 1000 requests/hour)
- **IP-based requests**: 100 requests per hour for non-authenticated requests

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Total requests allowed per hour
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Unix timestamp when the rate limit resets

## API Endpoints

### Authentication

#### Register User
```http
POST /api/v1/register
Content-Type: application/json

{
    "first_name": "<PERSON>",
    "last_name": "Do<PERSON>",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "gender": "male",
    "phone": "+1234567890"
}
```

#### Login
```http
POST /api/v1/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### Get User Info
```http
GET /api/v1/me
Authorization: Bearer {token}
```

#### Logout
```http
POST /api/v1/logout
Authorization: Bearer {token}
```

### API Key Management

#### List API Keys
```http
GET /api/v1/api-keys
Authorization: Bearer {token}
```

#### Create API Key
```http
POST /api/v1/api-keys
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "My Integration",
    "permissions": ["rooms:read", "bookings:read"],
    "rate_limit": 1000,
    "expires_at": "2024-12-31T23:59:59Z",
    "allowed_ips": ["***********", "********"]
}
```

#### Update API Key
```http
PUT /api/v1/api-keys/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Updated Integration",
    "is_active": true
}
```

#### Delete API Key
```http
DELETE /api/v1/api-keys/{id}
Authorization: Bearer {token}
```

### Rooms

#### List Rooms (Public)
```http
GET /api/v1/rooms?page=1&per_page=15&search=luxury&gender=male&min_price=100&max_price=500
```

Query Parameters:
- `page`: Page number (default: 1)
- `per_page`: Items per page (max: 100, default: 15)
- `search`: Search in title, description, address, room number
- `is_available`: Filter by availability (true/false)
- `is_online`: Filter by online status (true/false)
- `gender`: Filter by gender (male/female/mixed)
- `university_id`: Filter by university ID
- `type_room_id`: Filter by room type ID
- `min_price`: Minimum price filter
- `max_price`: Maximum price filter
- `sort_by`: Sort field (created_at, Price, Rate, Title, RoomNumber)
- `sort_order`: Sort order (asc/desc)

#### Get Room Details (Public)
```http
GET /api/v1/rooms/{id}
```

#### List Available Rooms (Public)
```http
GET /api/v1/rooms/filter/available
```

#### List Rooms with API Key
```http
GET /api/v1/external/rooms
X-API-Key: your-api-key
X-API-Secret: your-api-secret
```

#### Create Room (Admin)
```http
POST /api/v1/admin/rooms
Authorization: Bearer {admin-token}
X-API-Key: admin-api-key
Content-Type: application/json

{
    "RoomNumber": "R001",
    "Title": "Luxury Studio",
    "gender": "mixed",
    "type_room_id": 1,
    "university_id": 1,
    "Address": "123 Main St",
    "Price": 500,
    "Description": "Beautiful studio apartment",
    "Rate": 4.5,
    "Photo": "room1.jpg"
}
```

### Bookings

#### Create Booking (Guest)
```http
POST /api/v1/bookings
Content-Type: application/json

{
    "room_id": 1,
    "check_in_date": "2024-01-15",
    "duration_months": 6,
    "guest_first_name": "Jane",
    "guest_last_name": "Smith",
    "guest_email": "<EMAIL>",
    "guest_phone": "+1234567890",
    "guest_gender": "female",
    "special_requests": "Ground floor preferred",
    "deposit_amount": 250
}
```

#### List User Bookings
```http
GET /api/v1/my-bookings?status=pending&page=1
Authorization: Bearer {token}
```

#### Get Booking Details
```http
GET /api/v1/bookings/{id}
Authorization: Bearer {token}
```

#### Update Booking
```http
PUT /api/v1/bookings/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "check_in_date": "2024-01-20",
    "duration_months": 8,
    "special_requests": "Updated request"
}
```

#### Cancel Booking
```http
POST /api/v1/bookings/{id}/cancel
Authorization: Bearer {token}
Content-Type: application/json

{
    "reason": "Change of plans"
}
```

#### Confirm Booking (Admin)
```http
POST /api/v1/admin/bookings/{id}/confirm
Authorization: Bearer {admin-token}
X-API-Key: admin-api-key
```

### External API (API Key Required)

#### List Bookings with API Key
```http
GET /api/v1/external/bookings?status=confirmed
X-API-Key: your-api-key
X-API-Secret: your-api-secret
```

#### Create Booking with API Key
```http
POST /api/v1/external/bookings
X-API-Key: your-api-key
X-API-Secret: your-api-secret
Content-Type: application/json

{
    "room_id": 1,
    "check_in_date": "2024-01-15",
    "duration_months": 6,
    "guest_first_name": "API",
    "guest_last_name": "User",
    "guest_email": "<EMAIL>",
    "guest_phone": "+1234567890",
    "guest_gender": "other"
}
```

## Response Format

All API responses follow this format:

### Success Response
```json
{
    "success": true,
    "data": {...},
    "message": "Optional success message"
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "errors": {...} // Validation errors (if applicable)
}
```

### Pagination Response
```json
{
    "success": true,
    "data": [...],
    "pagination": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 15,
        "total": 75,
        "from": 1,
        "to": 15
    }
}
```

## Error Codes

- `400` - Bad Request
- `401` - Unauthorized (missing or invalid authentication)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limit Exceeded
- `500` - Internal Server Error

## API Permissions

When creating API keys, you can assign the following permissions:

- `rooms:read` - Read access to rooms
- `rooms:write` - Create/update rooms (admin only)
- `bookings:read` - Read access to bookings
- `bookings:write` - Create/update bookings
- `admin` - Full administrative access
- `*` - All permissions

## Getting Started

1. **Register a user account** using the `/register` endpoint
2. **Login** to get your authentication token
3. **Create an API key** with appropriate permissions
4. **Use the API key** for external integrations
5. **Test the endpoints** using the provided examples

## Testing with Postman

A Postman collection is available in the `postman_collection.json` file. Import this collection into Postman to test all API endpoints.

### Environment Variables for Postman:
- `base_url`: Your API base URL (e.g., `http://localhost:8000/api/v1`)
- `auth_token`: Your Sanctum authentication token
- `api_key`: Your API key for external endpoints
- `api_secret`: Your API secret

## Support

For API support, please contact the development team or refer to the source code documentation.
