<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_keys', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Name/description for the API key
            $table->string('key', 64)->unique(); // The actual API key
            $table->string('secret', 128); // API secret for additional security
            $table->unsignedBigInteger('user_id')->nullable(); // Associated user (optional)
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->json('permissions')->nullable(); // JSON array of permissions
            $table->json('allowed_ips')->nullable(); // JSON array of allowed IP addresses
            $table->integer('rate_limit')->default(1000); // Requests per hour
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_keys');
    }
};
