<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ApiKeyController;
use App\Http\Controllers\Api\RoomApiController;
use App\Http\Controllers\Api\BookingApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public authentication routes
Route::prefix('v1')->middleware(['api.security', 'api.rate_limit'])->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'loginAPI']);

    // Public room browsing (limited access)
    Route::get('/rooms', [RoomApiController::class, 'index']);
    Route::get('/rooms/{id}', [RoomApiController::class, 'show']);
    Route::get('/rooms/filter/available', [RoomApiController::class, 'available']);

    // Guest booking (no authentication required)
    Route::post('/bookings', [BookingApiController::class, 'store']);
});

// Authenticated user routes (Sanctum token required)
Route::prefix('v1')->middleware(['auth:sanctum', 'api.security', 'api.rate_limit'])->group(function () {
    // Authentication
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);

    // API Key Management
    Route::apiResource('api-keys', ApiKeyController::class);
    Route::post('/api-keys/{id}/regenerate-secret', [ApiKeyController::class, 'regenerateSecret']);

    // User bookings
    Route::get('/my-bookings', [BookingApiController::class, 'index']);
    Route::get('/bookings/{id}', [BookingApiController::class, 'show']);
    Route::put('/bookings/{id}', [BookingApiController::class, 'update']);
    Route::post('/bookings/{id}/cancel', [BookingApiController::class, 'cancel']);
});

// API Key authenticated routes (for external integrations)
Route::prefix('v1')->middleware(['api.key:rooms:read', 'api.security', 'api.rate_limit'])->group(function () {
    // Room access with API key
    Route::get('/external/rooms', [RoomApiController::class, 'index']);
    Route::get('/external/rooms/{id}', [RoomApiController::class, 'show']);
    Route::get('/external/rooms/filter/available', [RoomApiController::class, 'available']);
    Route::get('/external/rooms/filter/booked', [RoomApiController::class, 'booked']);
});

Route::prefix('v1')->middleware(['api.key:bookings:read', 'api.security', 'api.rate_limit'])->group(function () {
    // Booking access with API key
    Route::get('/external/bookings', [BookingApiController::class, 'index']);
    Route::get('/external/bookings/{id}', [BookingApiController::class, 'show']);
});

Route::prefix('v1')->middleware(['api.key:bookings:write', 'api.security', 'api.rate_limit'])->group(function () {
    // Booking creation with API key
    Route::post('/external/bookings', [BookingApiController::class, 'store']);
    Route::put('/external/bookings/{id}', [BookingApiController::class, 'update']);
    Route::post('/external/bookings/{id}/cancel', [BookingApiController::class, 'cancel']);
});

// Admin routes (requires admin permissions)
Route::prefix('v1')->middleware(['auth:sanctum', 'api.key:admin', 'api.security', 'api.rate_limit'])->group(function () {
    // Room management
    Route::post('/admin/rooms', [RoomApiController::class, 'store']);
    Route::put('/admin/rooms/{id}', [RoomApiController::class, 'update']);
    Route::delete('/admin/rooms/{id}', [RoomApiController::class, 'destroy']);

    // Booking management
    Route::post('/admin/bookings/{id}/confirm', [BookingApiController::class, 'confirm']);
    Route::get('/admin/bookings', [BookingApiController::class, 'index']);
});

// Legacy routes (for backward compatibility)
Route::post('/loginAPI', [AuthController::class, 'loginAPI']);
Route::post('/logoutAPI', [AuthController::class, 'logout'])->middleware('auth:sanctum');
