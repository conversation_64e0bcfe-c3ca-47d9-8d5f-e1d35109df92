<!-- filepath: public/auth.html -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Authentication</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.4/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-DQvkBjpPgn7RC31MCQoOeC9TI2kdqa4+BSgNMNj8v77fdC77Kj5zpWFTJaaAoMbC" crossorigin="anonymous">
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="card col-md-6 offset-3 mt-5">
                <h1 class="text-center">API Authentication</h1>
                <div class="card-body ">
                    <form id="loginForm">
                        <div class="row mb-3">
                          <label for="inputEmail3" class="col-sm-2 col-form-label">Email</label>
                          <div class="col-sm-10">
                            <input type="email" class="form-control" id="email" name="email">
                          </div>
                        </div>
                        <div class="row mb-3">
                          <label for="inputPassword3" class="col-sm-2 col-form-label">Password</label>
                          <div class="col-sm-10">
                            <input type="password" class="form-control" id="password" name="password" >
                          </div>
                        </div>
                       
                        <button type="submit" class="btn btn-primary offset-5">Get Access </button>
                    </form>
                </div>
             
                
            </div>
        </div>
    </div>

  
    

    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.4/dist/js/bootstrap.bundle.min.js" integrity="sha384-YUe2LzesAfftltw+PEaao2tjU/QATaW/rOitAq67e0CT0Zi2VVRL0oC4+gAaeBKu" crossorigin="anonymous"></script>
    <script>
        let token = '';

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            const response = await fetch('http://localhost:8000/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password }),
            });

            const data = await response.json();
            if (response.ok) {
                token = data.token;
                alert('Login successful! Token saved.');
            } else {
                alert(data.message || 'Login failed');
            }
        });

        document.getElementById('testApi').addEventListener('click', async () => {
            if (!token) {
                alert('Please log in first.');
                return;
            }

            const response = await fetch('http://localhost:8000/api/rooms', {
                method: 'GET',
                headers: { Authorization: `Bearer ${token}` },
            });

            const data = await response.json();
            if (response.ok) {
                console.log('API Response:', data);
                alert('API call successful! Check console for response.');
            } else {
                alert(data.message || 'API call failed');
            }
        });
    </script>
</body>
</html>