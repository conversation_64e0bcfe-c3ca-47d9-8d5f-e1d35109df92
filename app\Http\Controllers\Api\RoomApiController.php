<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Rooms;
use App\Models\University;
use App\Models\TypeRoom;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class RoomApiController extends Controller
{
    /**
     * Display a listing of rooms with filtering and pagination
     */
    public function index(Request $request)
    {
        try {
            $query = Rooms::with(['university', 'type_room']);

            // Apply filters
            if ($request->has('is_available')) {
                $query->where('IsAvailable', $request->boolean('is_available') ? 1 : 0);
            }

            if ($request->has('is_online')) {
                $query->where('IsOnline', $request->boolean('is_online') ? 1 : 0);
            }

            if ($request->has('gender')) {
                $query->where('gender', $request->gender);
            }

            if ($request->has('university_id')) {
                $query->where('university_id', $request->university_id);
            }

            if ($request->has('type_room_id')) {
                $query->where('type_room_id', $request->type_room_id);
            }

            if ($request->has('min_price')) {
                $query->where('Price', '>=', $request->min_price);
            }

            if ($request->has('max_price')) {
                $query->where('Price', '<=', $request->max_price);
            }

            // Search functionality
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('Title', 'like', "%{$search}%")
                      ->orWhere('Description', 'like', "%{$search}%")
                      ->orWhere('Address', 'like', "%{$search}%")
                      ->orWhere('RoomNumber', 'like', "%{$search}%");
                });
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            
            $allowedSortFields = ['created_at', 'Price', 'Rate', 'Title', 'RoomNumber'];
            if (in_array($sortBy, $allowedSortFields)) {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Pagination
            $perPage = min($request->get('per_page', 15), 100); // Max 100 items per page
            $rooms = $query->paginate($perPage);

            // Transform the data
            $rooms->getCollection()->transform(function ($room) {
                return $this->transformRoom($room);
            });

            return response()->json([
                'success' => true,
                'data' => $rooms->items(),
                'pagination' => [
                    'current_page' => $rooms->currentPage(),
                    'last_page' => $rooms->lastPage(),
                    'per_page' => $rooms->perPage(),
                    'total' => $rooms->total(),
                    'from' => $rooms->firstItem(),
                    'to' => $rooms->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch rooms',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified room
     */
    public function show($id)
    {
        try {
            $room = Rooms::with(['university', 'type_room', 'portfolio'])->find($id);

            if (!$room) {
                return response()->json([
                    'success' => false,
                    'message' => 'Room not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $this->transformRoom($room, true)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch room',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created room (requires admin permissions)
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'RoomNumber' => 'required|string|unique:rooms,RoomNumber',
                'Title' => 'required|string|max:255',
                'gender' => 'required|string|in:male,female,mixed',
                'type_room_id' => 'required|exists:type_rooms,id',
                'university_id' => 'required|exists:universities,id',
                'Address' => 'required|string',
                'Price' => 'required|numeric|min:0',
                'Description' => 'nullable|string',
                'Rate' => 'nullable|numeric|min:1|max:5',
                'Photo' => 'nullable|string',
                'logo_university' => 'nullable|string',
                'univ_around_available' => 'nullable|array',
            ]);

            $room = Rooms::create([
                'RoomNumber' => $validatedData['RoomNumber'],
                'Title' => $validatedData['Title'],
                'gender' => $validatedData['gender'],
                'type_room_id' => $validatedData['type_room_id'],
                'university_id' => $validatedData['university_id'],
                'Address' => $validatedData['Address'],
                'Price' => $validatedData['Price'],
                'Description' => $validatedData['Description'] ?? null,
                'Rate' => $validatedData['Rate'] ?? 1,
                'Photo' => $validatedData['Photo'] ?? null,
                'logo_university' => $validatedData['logo_university'] ?? null,
                'IsAvailable' => 0, // Available by default
                'IsOnline' => 1, // Online by default
                'univ_around_available' => isset($validatedData['univ_around_available']) 
                    ? json_encode($validatedData['univ_around_available']) 
                    : null,
            ]);

            $room->load(['university', 'type_room']);

            return response()->json([
                'success' => true,
                'message' => 'Room created successfully',
                'data' => $this->transformRoom($room)
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create room',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified room (requires admin permissions)
     */
    public function update(Request $request, $id)
    {
        try {
            $room = Rooms::find($id);

            if (!$room) {
                return response()->json([
                    'success' => false,
                    'message' => 'Room not found'
                ], 404);
            }

            $validatedData = $request->validate([
                'RoomNumber' => 'string|unique:rooms,RoomNumber,' . $id,
                'Title' => 'string|max:255',
                'gender' => 'string|in:male,female,mixed',
                'type_room_id' => 'exists:type_rooms,id',
                'university_id' => 'exists:universities,id',
                'Address' => 'string',
                'Price' => 'numeric|min:0',
                'Description' => 'nullable|string',
                'Rate' => 'nullable|numeric|min:1|max:5',
                'Photo' => 'nullable|string',
                'logo_university' => 'nullable|string',
                'IsAvailable' => 'boolean',
                'IsOnline' => 'boolean',
                'univ_around_available' => 'nullable|array',
            ]);

            // Handle univ_around_available encoding
            if (isset($validatedData['univ_around_available'])) {
                $validatedData['univ_around_available'] = json_encode($validatedData['univ_around_available']);
            }

            $room->update($validatedData);
            $room->load(['university', 'type_room']);

            return response()->json([
                'success' => true,
                'message' => 'Room updated successfully',
                'data' => $this->transformRoom($room)
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update room',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified room (requires admin permissions)
     */
    public function destroy($id)
    {
        try {
            $room = Rooms::find($id);

            if (!$room) {
                return response()->json([
                    'success' => false,
                    'message' => 'Room not found'
                ], 404);
            }

            $room->delete();

            return response()->json([
                'success' => true,
                'message' => 'Room deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete room',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available rooms only
     */
    public function available(Request $request)
    {
        $request->merge(['is_available' => false]); // IsAvailable = 0 means available
        return $this->index($request);
    }

    /**
     * Get booked rooms only
     */
    public function booked(Request $request)
    {
        $request->merge(['is_available' => true]); // IsAvailable = 1 means booked
        return $this->index($request);
    }

    /**
     * Transform room data for API response
     */
    private function transformRoom($room, $detailed = false)
    {
        $data = [
            'id' => $room->id,
            'room_number' => $room->RoomNumber,
            'title' => $room->Title,
            'gender' => $room->gender,
            'address' => $room->Address,
            'price' => (float) $room->Price,
            'rate' => (float) $room->Rate,
            'is_available' => !$room->IsAvailable, // Invert because 0 = available
            'is_online' => (bool) $room->IsOnline,
            'photo_url' => $room->Photo ? asset('uploads/images/' . $room->Photo) : null,
            'university' => $room->university ? [
                'id' => $room->university->id,
                'name' => $room->university->name ?? 'Unknown',
            ] : null,
            'type_room' => $room->type_room ? [
                'id' => $room->type_room->id,
                'name' => $room->type_room->Name ?? 'Unknown',
            ] : null,
            'created_at' => $room->created_at,
            'updated_at' => $room->updated_at,
        ];

        if ($detailed) {
            $data['description'] = $room->Description;
            $data['logo_university_url'] = $room->logo_university ? asset('uploads/images/' . $room->logo_university) : null;
            $data['universities_around'] = $room->univ_around_available ? json_decode($room->univ_around_available, true) : [];
            
            // Add portfolio/gallery if available
            if ($room->portfolio) {
                $data['gallery'] = $room->portfolio->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'photo_url' => $item->Photo ? asset('uploads/images/' . $item->Photo) : null,
                    ];
                });
            }
        }

        return $data;
    }
}
