<?php

namespace App\Http\Controllers;

use App\Models\ApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ApiKeyController extends Controller
{
    /**
     * Display a listing of API keys for the authenticated user
     */
    public function index()
    {
        $apiKeys = ApiKey::where('user_id', Auth::id())
                        ->select(['id', 'name', 'key', 'permissions', 'rate_limit', 'last_used_at', 'expires_at', 'is_active', 'created_at'])
                        ->orderBy('created_at', 'desc')
                        ->get();

        return response()->json([
            'success' => true,
            'data' => $apiKeys
        ]);
    }

    /**
     * Create a new API key
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'permissions' => 'array',
            'permissions.*' => 'string',
            'expires_at' => 'nullable|date|after:now',
            'rate_limit' => 'integer|min:1|max:10000',
            'allowed_ips' => 'array',
            'allowed_ips.*' => 'ip'
        ]);

        $expiresAt = $request->expires_at ? Carbon::parse($request->expires_at) : null;

        $apiKey = ApiKey::generate(
            $request->name,
            Auth::id(),
            $request->permissions ?? ['rooms:read'],
            $expiresAt
        );

        if ($request->rate_limit) {
            $apiKey->update(['rate_limit' => $request->rate_limit]);
        }

        if ($request->allowed_ips) {
            $apiKey->update(['allowed_ips' => $request->allowed_ips]);
        }

        return response()->json([
            'success' => true,
            'message' => 'API key created successfully',
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'key' => $apiKey->key,
                'secret' => $apiKey->secret, // Only shown once during creation
                'permissions' => $apiKey->permissions,
                'rate_limit' => $apiKey->rate_limit,
                'expires_at' => $apiKey->expires_at,
                'created_at' => $apiKey->created_at
            ]
        ], 201);
    }

    /**
     * Show a specific API key
     */
    public function show($id)
    {
        $apiKey = ApiKey::where('user_id', Auth::id())
                       ->where('id', $id)
                       ->select(['id', 'name', 'key', 'permissions', 'allowed_ips', 'rate_limit', 'last_used_at', 'expires_at', 'is_active', 'created_at'])
                       ->first();

        if (!$apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API key not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $apiKey
        ]);
    }

    /**
     * Update an API key
     */
    public function update(Request $request, $id)
    {
        $apiKey = ApiKey::where('user_id', Auth::id())->where('id', $id)->first();

        if (!$apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API key not found'
            ], 404);
        }

        $request->validate([
            'name' => 'string|max:255',
            'permissions' => 'array',
            'permissions.*' => 'string',
            'expires_at' => 'nullable|date|after:now',
            'rate_limit' => 'integer|min:1|max:10000',
            'allowed_ips' => 'array',
            'allowed_ips.*' => 'ip',
            'is_active' => 'boolean'
        ]);

        $updateData = $request->only(['name', 'permissions', 'rate_limit', 'allowed_ips', 'is_active']);
        
        if ($request->has('expires_at')) {
            $updateData['expires_at'] = $request->expires_at ? Carbon::parse($request->expires_at) : null;
        }

        $apiKey->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'API key updated successfully',
            'data' => $apiKey->fresh(['id', 'name', 'key', 'permissions', 'allowed_ips', 'rate_limit', 'last_used_at', 'expires_at', 'is_active'])
        ]);
    }

    /**
     * Delete an API key
     */
    public function destroy($id)
    {
        $apiKey = ApiKey::where('user_id', Auth::id())->where('id', $id)->first();

        if (!$apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API key not found'
            ], 404);
        }

        $apiKey->delete();

        return response()->json([
            'success' => true,
            'message' => 'API key deleted successfully'
        ]);
    }

    /**
     * Regenerate API key secret
     */
    public function regenerateSecret($id)
    {
        $apiKey = ApiKey::where('user_id', Auth::id())->where('id', $id)->first();

        if (!$apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API key not found'
            ], 404);
        }

        $apiKey->update(['secret' => \Illuminate\Support\Str::random(64)]);

        return response()->json([
            'success' => true,
            'message' => 'API secret regenerated successfully',
            'data' => [
                'secret' => $apiKey->secret
            ]
        ]);
    }
}
