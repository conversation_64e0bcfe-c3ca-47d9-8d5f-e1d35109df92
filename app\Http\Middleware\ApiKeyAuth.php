<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\ApiKey;
use Symfony\Component\HttpFoundation\Response;

class ApiKeyAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        $apiKey = $request->header('X-API-Key');
        $apiSecret = $request->header('X-API-Secret');

        if (!$apiKey) {
            return response()->json([
                'error' => 'API key is required',
                'message' => 'Please provide X-API-Key header'
            ], 401);
        }

        $keyModel = ApiKey::where('key', $apiKey)->first();

        if (!$keyModel) {
            return response()->json([
                'error' => 'Invalid API key',
                'message' => 'The provided API key is not valid'
            ], 401);
        }

        if (!$keyModel->isValid()) {
            return response()->json([
                'error' => 'API key expired or inactive',
                'message' => 'The API key is either expired or has been deactivated'
            ], 401);
        }

        // Check API secret if provided
        if ($apiSecret && $keyModel->secret !== $apiSecret) {
            return response()->json([
                'error' => 'Invalid API secret',
                'message' => 'The provided API secret does not match'
            ], 401);
        }

        // Check IP restrictions
        $clientIp = $request->ip();
        if (!$keyModel->isIpAllowed($clientIp)) {
            return response()->json([
                'error' => 'IP not allowed',
                'message' => 'Your IP address is not authorized to use this API key'
            ], 403);
        }

        // Check permissions if specified
        if (!empty($permissions)) {
            foreach ($permissions as $permission) {
                if (!$keyModel->hasPermission($permission)) {
                    return response()->json([
                        'error' => 'Insufficient permissions',
                        'message' => "This API key does not have the required permission: {$permission}"
                    ], 403);
                }
            }
        }

        // Mark API key as used
        $keyModel->markAsUsed();

        // Add API key to request for later use
        $request->merge(['api_key' => $keyModel]);

        return $next($request);
    }
}
