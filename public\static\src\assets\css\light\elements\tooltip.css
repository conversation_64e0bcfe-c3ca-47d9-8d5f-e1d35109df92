/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.tooltip-inner {
  border-radius: 6px;
}

.tooltip .tooltip-item {
  color: #fff;
  padding: 0 9px;
}

.tooltip-section h6 {
  color: #3b3f5c;
  font-size: 0.875rem;
  margin-top: 25px;
  margin-bottom: 20px;
}

.tooltip .tooltip-inner {
  background-color: #060818;
}

.bs-tooltip-top .tooltip-arrow::before {
  border-top-color: #060818;
}

/*
    ==================
        Colors
    =================
*/
/*
    Tooltips
*/
/*		Tooltip Inner 	*/
.tooltip-primary .tooltip-inner {
  color: #4361ee;
  background-color: #eceffe;
}

.tooltip-success .tooltip-inner {
  color: #00ab55;
  background-color: #ddf5f0;
}

.tooltip-info .tooltip-inner {
  color: #2196f3;
  background-color: #e6f4ff;
}

.tooltip-danger .tooltip-inner {
  color: #e7515a;
  background-color: #fbeced;
}

.tooltip-warning .tooltip-inner {
  color: #e2a03f;
  background-color: #fcf5e9;
}

.tooltip-secondary .tooltip-inner {
  color: #805dca;
  background-color: #f2eafa;
}

.tooltip-dark .tooltip-inner {
  color: #3b3f5c;
  background-color: #eaeaec;
}

/*		Tooltip arrow 		*/
.tooltip.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: #060818;
}
.tooltip.bs-tooltip-bottom .tooltip-arrow:before {
  border-bottom-color: #060818;
}
.tooltip.bs-tooltip-left .tooltip-arrow:before {
  border-left-color: #060818;
}
.tooltip.bs-tooltip-right .tooltip-arrow:before {
  border-right-color: #060818;
}

.tooltip-primary.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: #eceffe;
}

.tooltip-info.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: #e6f4ff;
}

.tooltip-success.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: #ddf5f0;
}

.tooltip-warning.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: #fcf5e9;
}

.tooltip-danger.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: #fbeced;
}

.tooltip-secondary.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: #f2eafa;
}

.tooltip-dark.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: #eaeaec;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
