{"info": {"_postman_id": "uroom-api-collection", "name": "Uroom API Collection", "description": "Complete API collection for Uroom room management and booking system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"gender\": \"male\",\n    \"phone\": \"+1234567890\"\n}"}, "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.token) {", "        pm.environment.set('auth_token', response.data.token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}, {"name": "Get User Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/me", "host": ["{{base_url}}"], "path": ["me"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}}}]}, {"name": "API Key Management", "item": [{"name": "List API Keys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api-keys", "host": ["{{base_url}}"], "path": ["api-keys"]}}}, {"name": "Create API Key", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data.key) {", "        pm.environment.set('api_key', response.data.key);", "        pm.environment.set('api_secret', response.data.secret);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Integration\",\n    \"permissions\": [\"rooms:read\", \"bookings:read\", \"bookings:write\"],\n    \"rate_limit\": 1000\n}"}, "url": {"raw": "{{base_url}}/api-keys", "host": ["{{base_url}}"], "path": ["api-keys"]}}}]}, {"name": "Rooms", "item": [{"name": "List Rooms (Public)", "request": {"method": "GET", "url": {"raw": "{{base_url}}/rooms?page=1&per_page=10&search=&is_available=false", "host": ["{{base_url}}"], "path": ["rooms"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}, {"key": "search", "value": ""}, {"key": "is_available", "value": "false"}]}}}, {"name": "Get Room Details", "request": {"method": "GET", "url": {"raw": "{{base_url}}/rooms/1", "host": ["{{base_url}}"], "path": ["rooms", "1"]}}}, {"name": "List Available Rooms", "request": {"method": "GET", "url": {"raw": "{{base_url}}/rooms/filter/available", "host": ["{{base_url}}"], "path": ["rooms", "filter", "available"]}}}, {"name": "List Rooms (API Key)", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "X-API-Secret", "value": "{{api_secret}}"}], "url": {"raw": "{{base_url}}/external/rooms", "host": ["{{base_url}}"], "path": ["external", "rooms"]}}}]}, {"name": "Bookings", "item": [{"name": "Create Booking (Guest)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"room_id\": 1,\n    \"check_in_date\": \"2024-02-01\",\n    \"duration_months\": 6,\n    \"guest_first_name\": \"<PERSON>\",\n    \"guest_last_name\": \"<PERSON>\",\n    \"guest_email\": \"<EMAIL>\",\n    \"guest_phone\": \"+1234567890\",\n    \"guest_gender\": \"female\",\n    \"special_requests\": \"Ground floor preferred\"\n}"}, "url": {"raw": "{{base_url}}/bookings", "host": ["{{base_url}}"], "path": ["bookings"]}}}, {"name": "List My Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/my-bookings", "host": ["{{base_url}}"], "path": ["my-bookings"]}}}, {"name": "Get Booking Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/bookings/1", "host": ["{{base_url}}"], "path": ["bookings", "1"]}}}, {"name": "Cancel Booking", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Change of plans\"\n}"}, "url": {"raw": "{{base_url}}/bookings/1/cancel", "host": ["{{base_url}}"], "path": ["bookings", "1", "cancel"]}}}, {"name": "Create Booking (API Key)", "request": {"method": "POST", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}, {"key": "X-API-Secret", "value": "{{api_secret}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"room_id\": 1,\n    \"check_in_date\": \"2024-03-01\",\n    \"duration_months\": 3,\n    \"guest_first_name\": \"API\",\n    \"guest_last_name\": \"User\",\n    \"guest_email\": \"<EMAIL>\",\n    \"guest_phone\": \"+1234567890\",\n    \"guest_gender\": \"other\"\n}"}, "url": {"raw": "{{base_url}}/external/bookings", "host": ["{{base_url}}"], "path": ["external", "bookings"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000/api/v1", "type": "string"}]}