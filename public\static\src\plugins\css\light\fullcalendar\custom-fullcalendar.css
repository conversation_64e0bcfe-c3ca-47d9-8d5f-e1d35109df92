/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.calendar-container {
  padding: 30px 30px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
  box-shadow: 0 0 40px 0 rgba(94, 92, 154, 0.06);
}

.fc .fc-button-primary {
  background-color: #eceffe;
  border-color: #e0e6ed;
  letter-spacing: 1px;
  font-size: 14px;
  color: #191e3a;
}
.fc .fc-button-primary:not(:disabled).fc-button-active {
  background-color: #805dca;
  font-weight: 900;
  border-color: #e0e6ed;
}
.fc .fc-button-primary:hover, .fc .fc-button-primary:not(:disabled):active {
  background-color: #e0e6ed;
  color: #3b3f5c;
  border-color: #e0e6ed;
}
.fc .fc-button-primary:focus, .fc .fc-button-primary:active:focus {
  box-shadow: none !important;
}
.fc .fc-list-sticky .fc-list-day > * {
  background-color: #fff;
}
.fc .fc-daygrid-body {
  width: 100% !important;
}
.fc .fc-scrollgrid-section table {
  width: 100% !important;
}
.fc .fc-scrollgrid-section-body table {
  width: 100% !important;
}

.fc-theme-standard .fc-list-day-cushion {
  background-color: #fff;
}
.fc-theme-standard .fc-list {
  border: 1px solid #e0e6ed;
}

.fc .fc-button {
  border-radius: 8px;
  padding: 7px 20px;
  text-transform: capitalize;
}
.fc .fc-addEventButton-button {
  background-color: #4361ee;
  border-color: #4361ee;
  color: #fff;
  font-weight: 700;
  box-shadow: 0 10px 20px -10px rgba(27, 85, 226, 0.59);
}
.fc .fc-addEventButton-button:hover, .fc .fc-addEventButton-button:not(:disabled):active {
  background-color: #4361ee;
  border-color: #4361ee;
  box-shadow: none;
  color: #fff;
}

.fc-theme-standard .fc-scrollgrid, .fc-theme-standard td, .fc-theme-standard th {
  border: 1px solid #e0e6ed;
}

.fc-v-event .fc-event-main {
  color: #3b3f5c;
}

.fc-timegrid-event-harness-inset .fc-timegrid-event, .fc-timegrid-event.fc-event-mirror, .fc-timegrid-more-link {
  box-shadow: none;
}

.event-fc-color {
  background-color: #1b2e4b;
  border: none;
  padding: 4px 10px;
  margin-bottom: 1px;
  font-size: 13px;
  letter-spacing: 1px;
  font-weight: 300;
  cursor: pointer;
}
.event-fc-color:hover {
  background-color: #f1f2f3;
}

.fc .fc-daygrid-day.fc-day-today {
  background-color: transparent;
  padding: 3px;
  border-radius: 23px;
}
.fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-frame {
  background-color: #eaeaec;
  border-radius: 8px;
}
.fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-frame .fc-daygrid-day-number {
  font-size: 15px;
  font-weight: 800;
}

.fc-daygrid-event-dot {
  margin: 0 6px 0 0;
}

.fc-bg-primary {
  color: #4361ee;
  background-color: rgba(67, 97, 238, 0.15);
}
.fc-bg-primary.fc-h-event .fc-event-main {
  color: #4361ee;
}

.fc-bg-success {
  color: #00ab55;
  background-color: rgba(26, 188, 156, 0.15);
}
.fc-bg-success.fc-h-event .fc-event-main {
  color: #00ab55;
}

.fc-bg-warning {
  color: #e2a03f;
  background-color: rgba(226, 160, 63, 0.15);
}
.fc-bg-warning.fc-h-event .fc-event-main {
  color: #e2a03f;
}

.fc-bg-danger {
  color: #e7515a;
  background-color: rgba(231, 81, 90, 0.15);
}
.fc-bg-danger.fc-h-event .fc-event-main {
  color: #e7515a;
}

.fc-bg-primary .fc-daygrid-event-dot {
  border-color: #4361ee;
}

.fc-bg-success .fc-daygrid-event-dot {
  border-color: #00ab55;
}

.fc-bg-warning .fc-daygrid-event-dot {
  border-color: #e2a03f;
}

.fc-bg-danger .fc-daygrid-event-dot {
  border-color: #e7515a;
}

.fc .fc-list-event:hover td {
  background-color: #f1f2f3;
}

/* Modal CSS */
.btn-update-event {
  display: none;
}

@media (max-width: 1199px) {
  .calendar-container {
    padding: 30px 0 0 0;
  }
  .fc-theme-standard .fc-list {
    border: none;
  }
  .fc .fc-toolbar {
    align-items: center;
    flex-direction: column;
  }
  .fc-toolbar-chunk:not(:first-child) {
    margin-top: 35px;
  }
  .fc .fc-toolbar.fc-header-toolbar {
    margin-bottom: 50px;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4uL2Jhc2UvX2Z1bmN0aW9ucy5zY3NzIiwiLi4vYmFzZS9fbWl4aW5zLnNjc3MiLCJmdWxsY2FsZW5kYXIvY3VzdG9tLWZ1bGxjYWxlbmRhci5zY3NzIiwiLi4vYmFzZS9fY29sb3JfdmFyaWFibGVzLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQ0FBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUNFQTtFQUNFO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7OztBQUlBO0VBQ0U7RUFDQTtFQUNBO0VBQ0E7RUFDQTs7QUFFQTtFQUNFLGtCQ0xNO0VETU47RUFDQTs7QUFJRjtFQUNFO0VBQ0EsT0NaQztFRGFEOztBQUdGO0VBQ0U7O0FBSUo7RUFDRTs7QUFHRjtFQUNFOztBQUlBO0VBQ0U7O0FBS0Y7RUFDRTs7O0FBTUo7RUFDRTs7QUFHRjtFQUNFOzs7QUFLRjtFQUNFO0VBQ0E7RUFDQTs7QUFHRjtFQUNFLGtCQ2xFTTtFRG1FTixjQ25FTTtFRG9FTjtFQUNBO0VBQ0E7O0FBRUE7RUFDRSxrQkN6RUk7RUQwRUosY0MxRUk7RUQyRUo7RUFDQTs7O0FBTUo7RUFDRTs7O0FBSUo7RUFDRSxPQ2xGSzs7O0FEcUZQO0VBQ0U7OztBQUdGO0VBQ0U7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTs7QUFFQTtFQUNFOzs7QUFJSjtFQUNFO0VBQ0E7RUFDQTs7QUFFQTtFQUNFO0VBQ0E7O0FBRUE7RUFDRTtFQUNBOzs7QUFLTjtFQUNFOzs7QUFHRjtFQUNFLE9DbklRO0VEb0lSOztBQUVBO0VBQ0UsT0N2SU07OztBRDJJVjtFQUNFO0VBQ0E7O0FBRUE7RUFDRTs7O0FBSUo7RUFDRSxPQ2xKUTtFRG1KUjs7QUFFQTtFQUNFLE9DdEpNOzs7QUQwSlY7RUFDRSxPQzFKTztFRDJKUDs7QUFFQTtFQUNFLE9DOUpLOzs7QURrS1Q7RUFDRSxjQ3ZLUTs7O0FEMEtWO0VBQ0U7OztBQUdGO0VBQ0UsY0M1S1E7OztBRCtLVjtFQUNFLGNDL0tPOzs7QURrTFQ7RUFDRTs7O0FBR0Y7QUFFQTtFQUNFOzs7QUFHRjtFQUNFO0lBQ0U7O0VBR0Y7SUFDRTs7RUFHRjtJQUNFO0lBQ0E7O0VBR0Y7SUFDRTs7RUFHRjtJQUNFIiwiZmlsZSI6ImZ1bGxjYWxlbmRhci9jdXN0b20tZnVsbGNhbGVuZGFyLmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qXHJcblx0PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxyXG5cdFx0XHRASW1wb3J0XHRGdW5jdGlvblxyXG5cdD09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cclxuKi9cclxuXHJcbiIsIi8qXHJcblx0PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxyXG5cdFx0XHRASW1wb3J0XHRNaXhpbnNcclxuXHQ9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XHJcbiovXHJcblxyXG4vLyBCb3JkZXJcclxuJGRpcmVjdGlvbjogJyc7XHJcbkBtaXhpbiBib3JkZXIoJGRpcmVjdGlvbiwgJHdpZHRoLCAkc3R5bGUsICRjb2xvcikge1xyXG5cclxuICAgQGlmICRkaXJlY3Rpb24gPT0gJycge1xyXG4gICAgICAgIGJvcmRlcjogJHdpZHRoICRzdHlsZSAkY29sb3I7XHJcbiAgIH0gQGVsc2Uge1xyXG4gICAgICAgIGJvcmRlci0jeyRkaXJlY3Rpb259OiAkd2lkdGggJHN0eWxlICRjb2xvcjtcclxuICAgfVxyXG59IiwiQGltcG9ydCAnLi4vLi4vYmFzZS9iYXNlJztcclxuXHJcbi5jYWxlbmRhci1jb250YWluZXIge1xyXG4gIHBhZGRpbmc6IDMwcHggMzBweDtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZTBlNmVkO1xyXG4gIGJveC1zaGFkb3c6IDAgMCA0MHB4IDAgcmdiKDk0IDkyIDE1NCAvIDYlKTtcclxufVxyXG5cclxuLmZjIHtcclxuICAuZmMtYnV0dG9uLXByaW1hcnkge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2VjZWZmZTtcclxuICAgIGJvcmRlci1jb2xvcjogI2UwZTZlZDtcclxuICAgIGxldHRlci1zcGFjaW5nOiAxcHg7XHJcbiAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICBjb2xvcjogIzE5MWUzYTtcclxuXHJcbiAgICAmOm5vdCg6ZGlzYWJsZWQpLmZjLWJ1dHRvbi1hY3RpdmUge1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkc2Vjb25kYXJ5O1xyXG4gICAgICBmb250LXdlaWdodDogOTAwO1xyXG4gICAgICBib3JkZXItY29sb3I6ICNlMGU2ZWQ7XHJcbiAgICAgIFxyXG4gICAgfVxyXG5cclxuICAgICY6aG92ZXIsICY6bm90KDpkaXNhYmxlZCk6YWN0aXZlIHtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UwZTZlZDtcclxuICAgICAgY29sb3I6ICRkYXJrO1xyXG4gICAgICBib3JkZXItY29sb3I6ICNlMGU2ZWQ7XHJcbiAgICB9XHJcblxyXG4gICAgJjpmb2N1cywgJjphY3RpdmU6Zm9jdXMge1xyXG4gICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuZmMtbGlzdC1zdGlja3kgLmZjLWxpc3QtZGF5ID4gKiB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gIH1cclxuXHJcbiAgLmZjLWRheWdyaWQtYm9keSB7XHJcbiAgICB3aWR0aDogMTAwJSFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAuZmMtc2Nyb2xsZ3JpZC1zZWN0aW9uIHtcclxuICAgIHRhYmxlIHtcclxuICAgICAgd2lkdGg6IDEwMCUhaW1wb3J0YW50O1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmZjLXNjcm9sbGdyaWQtc2VjdGlvbi1ib2R5IHtcclxuICAgIHRhYmxlIHtcclxuICAgICAgd2lkdGg6IDEwMCUhaW1wb3J0YW50O1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmZjLXRoZW1lLXN0YW5kYXJkIHtcclxuICAuZmMtbGlzdC1kYXktY3VzaGlvbiB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gIH1cclxuXHJcbiAgLmZjLWxpc3Qge1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTZlZDtcclxuICB9XHJcbn1cclxuXHJcbi5mYyB7XHJcbiAgLmZjLWJ1dHRvbiB7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiA3cHggMjBweDtcclxuICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xyXG4gIH1cclxuXHJcbiAgLmZjLWFkZEV2ZW50QnV0dG9uLWJ1dHRvbiB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkcHJpbWFyeTtcclxuICAgIGJvcmRlci1jb2xvcjogJHByaW1hcnk7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICBib3gtc2hhZG93OiAwIDEwcHggMjBweCAtMTBweCByZ2IoMjcgODUgMjI2IC8gNTklKTtcclxuXHJcbiAgICAmOmhvdmVyLCAmOm5vdCg6ZGlzYWJsZWQpOmFjdGl2ZSB7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICRwcmltYXJ5O1xyXG4gICAgICBib3JkZXItY29sb3I6ICRwcmltYXJ5O1xyXG4gICAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgICBjb2xvcjogI2ZmZjtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5mYy10aGVtZS1zdGFuZGFyZCB7XHJcbiAgLmZjLXNjcm9sbGdyaWQsIHRkLCB0aCB7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTBlNmVkO1xyXG4gIH1cclxufVxyXG5cclxuLmZjLXYtZXZlbnQgLmZjLWV2ZW50LW1haW4ge1xyXG4gIGNvbG9yOiAkZGFyaztcclxufVxyXG5cclxuLmZjLXRpbWVncmlkLWV2ZW50LWhhcm5lc3MtaW5zZXQgLmZjLXRpbWVncmlkLWV2ZW50LCAuZmMtdGltZWdyaWQtZXZlbnQuZmMtZXZlbnQtbWlycm9yLCAuZmMtdGltZWdyaWQtbW9yZS1saW5rIHtcclxuICBib3gtc2hhZG93OiBub25lO1xyXG59XHJcblxyXG4uZXZlbnQtZmMtY29sb3Ige1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICMxYjJlNGI7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIHBhZGRpbmc6IDRweCAxMHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDFweDtcclxuICBmb250LXNpemU6IDEzcHg7XHJcbiAgbGV0dGVyLXNwYWNpbmc6IDFweDtcclxuICBmb250LXdlaWdodDogMzAwO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjFmMmYzO1xyXG4gIH1cclxufVxyXG5cclxuLmZjIC5mYy1kYXlncmlkLWRheS5mYy1kYXktdG9kYXkge1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gIHBhZGRpbmc6IDNweDtcclxuICBib3JkZXItcmFkaXVzOiAyM3B4O1xyXG5cclxuICAuZmMtZGF5Z3JpZC1kYXktZnJhbWUge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2VhZWFlYztcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuXHJcbiAgICAuZmMtZGF5Z3JpZC1kYXktbnVtYmVyIHtcclxuICAgICAgZm9udC1zaXplOiAxNXB4O1xyXG4gICAgICBmb250LXdlaWdodDogODAwO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmZjLWRheWdyaWQtZXZlbnQtZG90IHtcclxuICBtYXJnaW46IDAgNnB4IDAgMDtcclxufVxyXG5cclxuLmZjLWJnLXByaW1hcnkge1xyXG4gIGNvbG9yOiAkcHJpbWFyeTtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDY3LCA5NywgMjM4LCAwLjE1KTtcclxuXHJcbiAgJi5mYy1oLWV2ZW50IC5mYy1ldmVudC1tYWluIHtcclxuICAgIGNvbG9yOiAkcHJpbWFyeTtcclxuICB9XHJcbn1cclxuXHJcbi5mYy1iZy1zdWNjZXNzIHtcclxuICBjb2xvcjogIzAwYWI1NTtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI2LCAxODgsIDE1NiwgMC4xNSk7XHJcblxyXG4gICYuZmMtaC1ldmVudCAuZmMtZXZlbnQtbWFpbiB7XHJcbiAgICBjb2xvcjogIzAwYWI1NTtcclxuICB9XHJcbn1cclxuXHJcbi5mYy1iZy13YXJuaW5nIHtcclxuICBjb2xvcjogJHdhcm5pbmc7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyMjYsIDE2MCwgNjMsIDAuMTUpO1xyXG5cclxuICAmLmZjLWgtZXZlbnQgLmZjLWV2ZW50LW1haW4ge1xyXG4gICAgY29sb3I6ICR3YXJuaW5nO1xyXG4gIH1cclxufVxyXG5cclxuLmZjLWJnLWRhbmdlciB7XHJcbiAgY29sb3I6ICRkYW5nZXI7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyMzEsIDgxLCA5MCwgMC4xNSk7XHJcblxyXG4gICYuZmMtaC1ldmVudCAuZmMtZXZlbnQtbWFpbiB7XHJcbiAgICBjb2xvcjogJGRhbmdlcjtcclxuICB9XHJcbn1cclxuXHJcbi5mYy1iZy1wcmltYXJ5IC5mYy1kYXlncmlkLWV2ZW50LWRvdCB7XHJcbiAgYm9yZGVyLWNvbG9yOiAkcHJpbWFyeTtcclxufVxyXG5cclxuLmZjLWJnLXN1Y2Nlc3MgLmZjLWRheWdyaWQtZXZlbnQtZG90IHtcclxuICBib3JkZXItY29sb3I6ICMwMGFiNTU7XHJcbn1cclxuXHJcbi5mYy1iZy13YXJuaW5nIC5mYy1kYXlncmlkLWV2ZW50LWRvdCB7XHJcbiAgYm9yZGVyLWNvbG9yOiAkd2FybmluZztcclxufVxyXG5cclxuLmZjLWJnLWRhbmdlciAuZmMtZGF5Z3JpZC1ldmVudC1kb3Qge1xyXG4gIGJvcmRlci1jb2xvcjogJGRhbmdlcjtcclxufVxyXG5cclxuLmZjIC5mYy1saXN0LWV2ZW50OmhvdmVyIHRkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjFmMmYzO1xyXG59XHJcblxyXG4vKiBNb2RhbCBDU1MgKi9cclxuXHJcbi5idG4tdXBkYXRlLWV2ZW50IHtcclxuICBkaXNwbGF5OiBub25lO1xyXG59XHJcblxyXG5AbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7XHJcbiAgLmNhbGVuZGFyLWNvbnRhaW5lciB7XHJcbiAgICBwYWRkaW5nOiAzMHB4IDAgMCAwO1xyXG4gIH1cclxuXHJcbiAgLmZjLXRoZW1lLXN0YW5kYXJkIC5mYy1saXN0IHtcclxuICAgIGJvcmRlcjogbm9uZTtcclxuICB9XHJcblxyXG4gIC5mYyAuZmMtdG9vbGJhciB7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICB9XHJcblxyXG4gIC5mYy10b29sYmFyLWNodW5rOm5vdCg6Zmlyc3QtY2hpbGQpIHtcclxuICAgIG1hcmdpbi10b3A6IDM1cHg7XHJcbiAgfVxyXG5cclxuICAuZmMgLmZjLXRvb2xiYXIuZmMtaGVhZGVyLXRvb2xiYXIge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogNTBweDtcclxuICB9XHJcbn0iLCJcclxuLy9cdD09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cclxuLy9cdFx0XHRASW1wb3J0XHRDb2xvcnNcclxuLy9cdD09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cclxuXHJcblxyXG4kd2hpdGU6ICNmZmY7XHJcbiRibGFjazogIzAwMDtcclxuXHJcbiRwcmltYXJ5OiAjNDM2MWVlO1xyXG4kaW5mbzogIzIxOTZmMztcclxuJHN1Y2Nlc3M6ICMwMGFiNTU7XHJcbiR3YXJuaW5nOiAjZTJhMDNmO1xyXG4kZGFuZ2VyOiAjZTc1MTVhO1xyXG4kc2Vjb25kYXJ5OiAjODA1ZGNhO1xyXG4kZGFyazogIzNiM2Y1YztcclxuXHJcblxyXG4kbC1wcmltYXJ5OiAjZWNlZmZlO1xyXG4kbC1pbmZvOiAjZTZmNGZmO1xyXG4kbC1zdWNjZXNzOiAjZGRmNWYwO1xyXG4kbC13YXJuaW5nOiAjZmNmNWU5O1xyXG4kbC1kYW5nZXI6ICNmYmVjZWQ7XHJcbiRsLXNlY29uZGFyeTogI2YyZWFmYTtcclxuJGwtZGFyazogI2VhZWFlYztcclxuXHJcbi8vIFx0PT09PT09PT09PT09PT09PT1cclxuLy9cdFx0TW9yZSBDb2xvcnNcclxuLy9cdD09PT09PT09PT09PT09PT09XHJcblxyXG4kbS1jb2xvcl8wOiAjZmFmYWZhO1xyXG4kbS1jb2xvcl8xOiAjZjFmMmYzO1xyXG4kbS1jb2xvcl8yOiAjZWJlZGYyO1xyXG5cclxuJG0tY29sb3JfMzogI2UwZTZlZDtcclxuJG0tY29sb3JfNDogI2JmYzlkNDtcclxuJG0tY29sb3JfNTogI2QzZDNkMztcclxuXHJcbiRtLWNvbG9yXzY6ICM4ODhlYTg7XHJcbiRtLWNvbG9yXzc6ICM1MDY2OTA7XHJcblxyXG4kbS1jb2xvcl84OiAjNTU1NTU1O1xyXG4kbS1jb2xvcl85OiAjNTE1MzY1O1xyXG4kbS1jb2xvcl8xMTogIzYwN2Q4YjtcclxuXHJcbiRtLWNvbG9yXzEyOiAjMWIyZTRiO1xyXG4kbS1jb2xvcl8xODogIzE5MWUzYTtcclxuJG0tY29sb3JfMTA6ICMwZTE3MjY7XHJcblxyXG4kbS1jb2xvcl8xOTogIzA2MDgxODtcclxuJG0tY29sb3JfMTM6ICMyMmM3ZDU7XHJcbiRtLWNvbG9yXzE0OiAjMDA5Njg4O1xyXG5cclxuJG0tY29sb3JfMTU6ICNmZmJiNDQ7XHJcbiRtLWNvbG9yXzE2OiAjZTk1ZjJiO1xyXG4kbS1jb2xvcl8xNzogI2Y4NTM4ZDtcclxuXHJcbiRtLWNvbG9yXzIwOiAjNDQ1ZWRlO1xyXG4kbS1jb2xvcl8yMTogIzMwNGFjYTtcclxuXHJcblxyXG4kbS1jb2xvcl8yMjogIzAzMDMwNTtcclxuJG0tY29sb3JfMjM6ICMxNTE1MTY7XHJcbiRtLWNvbG9yXzI0OiAjNjFiNmNkO1xyXG4kbS1jb2xvcl8yNTogIzRjZDI2NTtcclxuXHJcbiRtLWNvbG9yXzI2OiAjN2QzMGNiO1xyXG4kbS1jb2xvcl8yNzogIzAwOGVmZjtcclxuXHJcblxyXG5cclxuXHJcbi8vXHQ9PT09PT09PT09PT09PT09PT09PT09PT1cclxuLy9cdFx0Q29sb3IgRGVmaW5hdGlvblxyXG4vL1x0PT09PT09PT09PT09PT09PT09PT09PT09XHJcblxyXG5cclxuJGJvZHktY29sb3I6ICRtLWNvbG9yXzE5OyJdfQ== */
