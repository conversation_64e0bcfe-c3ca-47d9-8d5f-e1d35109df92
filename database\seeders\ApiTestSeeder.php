<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\University;
use App\Models\TypeRoom;
use App\Models\Rooms;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;

class ApiTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'Administrator']);
        $userRole = Role::firstOrCreate(['name' => 'api_user']);
        $guestRole = Role::firstOrCreate(['name' => 'user']);

        // Create universities
        $universities = [
            ['name' => 'University of Technology'],
            ['name' => 'Business University'],
            ['name' => 'Arts & Sciences College'],
        ];

        foreach ($universities as $university) {
            University::firstOrCreate(['name' => $university['name']], $university);
        }

        // Create room types
        $roomTypes = [
            ['Name' => 'Single Room'],
            ['Name' => 'Double Room'],
            ['Name' => 'Studio Apartment'],
            ['Name' => 'Shared Room'],
        ];

        foreach ($roomTypes as $roomType) {
            TypeRoom::firstOrCreate($roomType);
        }

        // Create test users
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Admin',
                'last_name' => 'User',
                'gender' => 'male',
                'password' => Hash::make('password123'),
                'State' => 1,
                'phone' => '+1234567890',
                'role_id' => $adminRole->id,
            ]
        );
        $adminUser->assignRole($adminRole);

        $testUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Test',
                'last_name' => 'User',
                'gender' => 'female',
                'password' => Hash::make('password123'),
                'State' => 1,
                'phone' => '+1234567891',
                'role_id' => $userRole->id,
            ]
        );
        $testUser->assignRole($userRole);

        // Create sample rooms
        $rooms = [
            [
                'RoomNumber' => 'R001',
                'Title' => 'Luxury Single Room',
                'gender' => 'mixed',
                'type_room_id' => TypeRoom::where('Name', 'Single Room')->first()->id,
                'university_id' => University::where('name', 'University of Technology')->first()->id,
                'Address' => '123 Campus Street, Room 101',
                'Price' => '500',
                'Description' => 'A beautiful single room with modern amenities, perfect for students.',
                'Rate' => '4.5',
                'IsAvailable' => 0, // Available
                'IsOnline' => 1, // Online
            ],
            [
                'RoomNumber' => 'R002',
                'Title' => 'Cozy Double Room',
                'gender' => 'female',
                'type_room_id' => TypeRoom::where('Name', 'Double Room')->first()->id,
                'university_id' => University::where('name', 'Business University')->first()->id,
                'Address' => '456 Student Ave, Room 202',
                'Price' => '350',
                'Description' => 'Shared double room with excellent facilities and study area.',
                'Rate' => '4.2',
                'IsAvailable' => 0, // Available
                'IsOnline' => 1, // Online
            ],
            [
                'RoomNumber' => 'R003',
                'Title' => 'Modern Studio Apartment',
                'gender' => 'male',
                'type_room_id' => TypeRoom::where('Name', 'Studio Apartment')->first()->id,
                'university_id' => University::where('name', 'Arts & Sciences College')->first()->id,
                'Address' => '789 University Blvd, Apt 301',
                'Price' => '750',
                'Description' => 'Fully furnished studio apartment with kitchen and private bathroom.',
                'Rate' => '4.8',
                'IsAvailable' => 1, // Booked
                'IsOnline' => 1, // Online
            ],
            [
                'RoomNumber' => 'R004',
                'Title' => 'Budget Shared Room',
                'gender' => 'mixed',
                'type_room_id' => TypeRoom::where('Name', 'Shared Room')->first()->id,
                'university_id' => University::where('name', 'University of Technology')->first()->id,
                'Address' => '123 Campus Street, Room 401',
                'Price' => '250',
                'Description' => 'Affordable shared room option with basic amenities.',
                'Rate' => '3.8',
                'IsAvailable' => 0, // Available
                'IsOnline' => 1, // Online
            ],
            [
                'RoomNumber' => 'R005',
                'Title' => 'Premium Single Suite',
                'gender' => 'female',
                'type_room_id' => TypeRoom::where('Name', 'Single Room')->first()->id,
                'university_id' => University::where('name', 'Business University')->first()->id,
                'Address' => '456 Student Ave, Suite 501',
                'Price' => '650',
                'Description' => 'Premium single room with private bathroom and study desk.',
                'Rate' => '4.7',
                'IsAvailable' => 0, // Available
                'IsOnline' => 1, // Online
            ],
        ];

        foreach ($rooms as $room) {
            Rooms::firstOrCreate(['RoomNumber' => $room['RoomNumber']], $room);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Test users created:');
        $this->command->info('- Admin: <EMAIL> / password123');
        $this->command->info('- User: <EMAIL> / password123');
        $this->command->info('Sample rooms: ' . Rooms::count());
        $this->command->info('Universities: ' . University::count());
        $this->command->info('Room types: ' . TypeRoom::count());
    }
}
