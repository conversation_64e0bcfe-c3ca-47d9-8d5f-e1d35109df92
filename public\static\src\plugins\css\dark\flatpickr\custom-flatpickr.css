/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
body.dark .flatpickr-calendar {
  width: 336.875px;
  padding: 15px;
  -webkit-box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2);
  border: 1px solid #1b2e4b;
  background: #1b2e4b;
}
body.dark .flatpickr-calendar.open {
  display: inline-block;
  z-index: 900;
}
body.dark .flatpickr-calendar.arrowTop:before {
  border-bottom-color: #1b2e4b;
}
body.dark .flatpickr-calendar.arrowBottom:before {
  border-top-color: #1b2e4b;
}
body.dark .flatpickr-calendar:before {
  border-width: 9px;
}
body.dark .flatpickr-calendar:after {
  border-width: 0px;
}
body.dark .flatpickr-months .flatpickr-prev-month, body.dark .flatpickr-months .flatpickr-next-month {
  top: 8%;
  padding: 5px 13px;
  background: #1b2e4b;
  border-radius: 4px;
  height: 40px;
}
body.dark .flatpickr-months .flatpickr-prev-month svg, body.dark .flatpickr-months .flatpickr-next-month svg {
  fill: #bfc9d4;
}
body.dark .flatpickr-months .flatpickr-prev-month:hover svg, body.dark .flatpickr-months .flatpickr-next-month:hover svg {
  fill: #009688;
}
body.dark .flatpickr-current-month .numInputWrapper span.arrowUp:after {
  border-bottom-color: #bfc9d4;
}
body.dark .flatpickr-current-month .numInputWrapper span.arrowDown:after {
  border-top-color: #bfc9d4;
}
body.dark .flatpickr-day.today {
  border-color: #009688;
  color: #009688;
  font-weight: 700;
}
body.dark .flatpickr-current-month .flatpickr-monthDropdown-months {
  height: auto;
  border: 1px solid #3b3f5c;
  color: #bfc9d4;
  font-size: 15px;
  padding: 12px 16px;
  letter-spacing: 1px;
  font-weight: 700;
}
body.dark .flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
  background-color: #1b2e4b;
}
body.dark .flatpickr-current-month input.cur-year {
  height: auto;
  border: 1px solid #3b3f5c;
  border-left: none;
  color: #bfc9d4;
  font-size: 15px;
  padding: 13px 12px;
  letter-spacing: 1px;
  font-weight: 700;
}
body.dark .flatpickr-months .flatpickr-month {
  height: 76px;
}
body.dark .flatpickr-day.flatpickr-disabled {
  cursor: not-allowed;
  color: #e0e6ed;
}
body.dark .flatpickr-day.flatpickr-disabled:hover {
  cursor: not-allowed;
  color: #e0e6ed;
}
body.dark span.flatpickr-weekday {
  color: #888ea8;
}
body.dark .flatpickr-day {
  color: #888ea8;
  font-weight: 500;
}
body.dark .flatpickr-day.flatpickr-disabled {
  color: rgba(136, 142, 168, 0.22);
}
body.dark .flatpickr-day.flatpickr-disabled:hover {
  color: rgba(136, 142, 168, 0.22);
}
body.dark .flatpickr-day.prevMonthDay, body.dark .flatpickr-day.nextMonthDay {
  color: rgba(136, 142, 168, 0.22);
}
body.dark .flatpickr-day.notAllowed {
  color: rgba(136, 142, 168, 0.22);
}
body.dark .flatpickr-day.notAllowed.prevMonthDay, body.dark .flatpickr-day.notAllowed.nextMonthDay {
  color: rgba(136, 142, 168, 0.22);
}
body.dark .flatpickr-day.inRange, body.dark .flatpickr-day.prevMonthDay.inRange, body.dark .flatpickr-day.nextMonthDay.inRange, body.dark .flatpickr-day.today.inRange, body.dark .flatpickr-day.prevMonthDay.today.inRange, body.dark .flatpickr-day.nextMonthDay.today.inRange, body.dark .flatpickr-day:hover, body.dark .flatpickr-day.prevMonthDay:hover, body.dark .flatpickr-day.nextMonthDay:hover, body.dark .flatpickr-day:focus, body.dark .flatpickr-day.prevMonthDay:focus, body.dark .flatpickr-day.nextMonthDay:focus {
  background: #191e3a;
  border-color: #191e3a;
  -webkit-box-shadow: -5px 0 0 #191e3a, 5px 0 0 #191e3a;
  box-shadow: -5px 0 0 #191e3a, 5px 0 0 #191e3a;
}
body.dark .flatpickr-day.selected, body.dark .flatpickr-day.startRange, body.dark .flatpickr-day.endRange, body.dark .flatpickr-day.selected.inRange, body.dark .flatpickr-day.startRange.inRange, body.dark .flatpickr-day.endRange.inRange, body.dark .flatpickr-day.selected:focus, body.dark .flatpickr-day.startRange:focus, body.dark .flatpickr-day.endRange:focus, body.dark .flatpickr-day.selected:hover, body.dark .flatpickr-day.startRange:hover, body.dark .flatpickr-day.endRange:hover, body.dark .flatpickr-day.selected.prevMonthDay, body.dark .flatpickr-day.startRange.prevMonthDay, body.dark .flatpickr-day.endRange.prevMonthDay, body.dark .flatpickr-day.selected.nextMonthDay, body.dark .flatpickr-day.startRange.nextMonthDay, body.dark .flatpickr-day.endRange.nextMonthDay {
  background: #009688;
  color: #0e1726;
  border-color: #009688;
  font-weight: 700;
}
body.dark .flatpickr-time input {
  color: #bfc9d4;
}
body.dark .flatpickr-time input:hover {
  background: #0e1726;
}
body.dark .flatpickr-time .flatpickr-am-pm:hover, body.dark .flatpickr-time input:focus, body.dark .flatpickr-time .flatpickr-am-pm:focus {
  background: #0e1726;
}
body.dark .flatpickr-time .flatpickr-time-separator, body.dark .flatpickr-time .flatpickr-am-pm {
  color: #bfc9d4;
}
body.dark .flatpickr-time .numInputWrapper span.arrowUp:after {
  border-bottom-color: #009688;
}
body.dark .flatpickr-time .numInputWrapper span.arrowDown:after {
  border-top-color: #009688;
}
@supports (-webkit-overflow-scrolling: touch) {
  body.dark .form-control {
    height: auto;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
