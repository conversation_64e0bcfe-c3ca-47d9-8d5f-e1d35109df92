/*
	===============================
			@Import	Function
	===============================
*/
/*
	===============================
			@Import	Mixins
	===============================
*/
.selectable-dropdown a.dropdown-toggle {
  padding: 11px 35px 10px 15px;
  position: relative;
  padding: 9px 8px 10px 12px;
  border-radius: 8px;
  transform: none;
  font-size: 13px;
  line-height: 17px;
  background-color: #fff;
  letter-spacing: normal;
  text-align: inherit;
  color: #506690;
  box-shadow: none;
  max-height: 35px;
  display: inline-block;
  cursor: pointer;
  width: 100%;
  border: 1px solid #bfc9d4;
}
.selectable-dropdown a.dropdown-toggle img {
  width: 19px;
  height: 19px;
  vertical-align: text-bottom;
  position: absolute;
  left: 12px;
  top: 7px;
}
.selectable-dropdown a.dropdown-toggle .selectable-text {
  overflow: hidden;
  display: block;
}
.selectable-dropdown a.dropdown-toggle .selectable-arrow {
  display: inline-block;
  position: absolute;
  padding: 6px 4px;
  background: #fff;
  top: 2px;
  right: 1px;
}
.selectable-dropdown a.dropdown-toggle svg {
  color: #888ea8;
  width: 13px !important;
  height: 13px !important;
  margin: 0;
  -webkit-transition: -webkit-transform 0.2s ease-in-out;
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
}
.selectable-dropdown a.dropdown-toggle.show svg {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.selectable-dropdown.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu {
  right: auto;
  top: 50px !important;
}
.selectable-dropdown.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu.show {
  top: 38px !important;
}
.selectable-dropdown.dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu img {
  width: 19px;
  height: 19px;
  margin-right: 7px;
  vertical-align: top;
}

.invoice-detail-body {
  padding: 0;
  padding-top: 32px;
  padding-bottom: 32px;
  background-color: #fff;
  border: 1px solid #e0e6ed;
  box-shadow: none;
  border-radius: 8px;
}

/*
====================
    Detail Body
====================
*/
/* Detail Title */
.invoice-content .invoice-detail-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
  padding: 0 48px;
}
.invoice-content .invoice-title input {
  font-size: 18px;
  padding: 5px 15px;
  height: auto;
}
.invoice-content .invoice-logo .dropify-wrapper {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  padding: 7px;
  border: 1px solid #1b2e4b;
  background: #1b2e4b;
}
.invoice-content .invoice-logo .dropify-wrapper .dropify-preview {
  background-color: #1b2e4b;
  padding: 0;
}
.invoice-content .invoice-logo .dropify-wrapper .dropify-clear {
  font-size: 10px;
  padding: 4px 8px;
  color: #bfc9d4;
  border: none;
  top: -3px;
  right: 0;
}
.invoice-content .invoice-logo .dropify-wrapper .dropify-clear:hover {
  background-color: transparent;
}
.invoice-content .invoice-logo .dropify-wrapper .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-infos-message {
  padding-top: 27px;
}
.invoice-content .invoice-logo .dropify-wrapper .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-infos-message::before {
  height: 20px;
  position: absolute;
  top: -1px;
  left: 45%;
  color: #fff;
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  background: transparent;
  width: 0;
  height: 0;
  font-size: 28px;
  width: 24px;
  content: " ";
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-upload-cloud'%3e%3cpolyline points='16 16 12 12 8 16'%3e%3c/polyline%3e%3cline x1='12' y1='12' x2='12' y2='21'%3e%3c/line%3e%3cpath d='M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3'%3e%3c/path%3e%3cpolyline points='16 16 12 12 8 16'%3e%3c/polyline%3e%3c/svg%3e");
  height: 20px;
}
.invoice-content .invoice-logo .dropify-wrapper.touch-fallback .dropify-preview .dropify-infos .dropify-infos-inner {
  padding: 0;
}
.invoice-content .invoice-logo .dropify-wrapper.touch-fallback .dropify-clear {
  color: #888ea8;
  position: relative;
}
.invoice-content .invoice-logo .dropify-wrapper.touch-fallback .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-filename {
  margin-top: 10px;
}
.invoice-content .invoice-detail-header {
  padding: 0 48px;
}
.invoice-content .invoice-address-company h4 {
  font-size: 18px;
  margin-bottom: 20px;
}
.invoice-content .invoice-address-company .invoice-address-company-fields label {
  font-size: 14px;
  color: #3b3f5c;
  min-width: 75px;
  align-self: center;
  margin-bottom: 0;
}
.invoice-content .invoice-address-company .invoice-address-company-fields .form-group {
  margin-bottom: 5px;
}
.invoice-content .invoice-address-client h4 {
  font-size: 18px;
  margin-bottom: 20px;
}
.invoice-content .invoice-address-client .invoice-address-client-fields label {
  font-size: 14px;
  color: #3b3f5c;
  min-width: 75px;
  align-self: center;
  margin-bottom: 0;
}
.invoice-content .invoice-address-client .invoice-address-client-fields .form-group {
  margin-bottom: 5px;
}

/* Detail Header */
/* Detail Header -> invoice-address-company */
/* Detail Header -> invoice-address-client */
/* Detail Terms */
.invoice-detail-terms {
  padding: 0 48px;
  padding-top: 25px;
  margin-top: 40px;
  border-top: 1px solid #e0e6ed;
}
.invoice-detail-terms label {
  font-size: 14px;
  color: #3b3f5c;
  min-width: 75px;
  align-self: center;
  margin-bottom: 0;
}

/* Detail Items */
.invoice-detail-items {
  background: #fff;
  padding: 30px;
  padding: 30px 48px;
}
.invoice-detail-items thead th {
  padding: 9px 6px;
  border: none;
  border-top: 1px solid #e0e6ed !important;
  border-bottom: 1px solid #e0e6ed !important;
  color: #3b3f5c !important;
  background: #fff !important;
  border-radius: 0 !important;
}
.invoice-detail-items tbody td {
  border: none;
  padding: 14px 7px;
  vertical-align: top !important;
  background: #fff !important;
}

/* Detail Items -> table thead */
/* Detail Items -> table body */
.delete-item-row {
  width: 10px;
}

.invoice-detail-items tbody td.description {
  width: 365px;
}
.invoice-detail-items tbody td.rate, .invoice-detail-items tbody td.qty {
  width: 110px;
}
.invoice-detail-items tbody td.amount {
  width: 60px;
}
.invoice-detail-items tbody td.tax {
  width: 60px;
}
.invoice-detail-items tbody td.tax .new-chk-content {
  display: none;
}
.invoice-detail-items tbody td ul {
  padding: 0;
}
.invoice-detail-items tbody td ul li {
  list-style: none;
}
.invoice-detail-items tbody td ul li svg {
  color: #888ea8;
  stroke-width: 1.5;
  height: 19px;
  width: 19px;
}
.invoice-detail-items tbody td textarea {
  margin-top: 5px;
  resize: none;
}
.invoice-detail-items tbody td span.editable-amount {
  white-space: nowrap;
}

/* Detail Items -> Editable amount */
/* Detail Total */
.invoice-detail-total {
  padding: 0 48px;
  margin-top: 25px;
}
.invoice-detail-total .invoice-created-by {
  margin-bottom: 5px;
}
.invoice-detail-total .invoice-created-by label {
  font-size: 14px;
  color: #3b3f5c;
  min-width: 75px;
  align-self: center;
  margin-bottom: 0;
}

/* Detail Total -> invoice-totals-row */
.totals-row {
  max-width: 11rem;
  margin-left: auto;
  margin-right: 60px;
}

.invoice-totals-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.invoice-totals-row .invoice-summary-label {
  min-width: 130px;
  min-width: 60px;
  font-size: 14px;
  color: #3b3f5c;
}
.invoice-totals-row .invoice-summary-value {
  min-width: 60px;
  text-align: right;
  font-size: 14px;
  color: #888ea8;
  font-weight: 600;
}
.invoice-totals-row.invoice-summary-balance-due {
  padding-top: 5px;
  margin-top: 5px;
  border-top: 1px solid #e0e6ed;
}
.invoice-totals-row.invoice-summary-balance-due .invoice-summary-label {
  font-size: 14px;
  color: #fff;
}

/* Detail Total -> invoice-summary-balance-due */
/* Detail Note */
.invoice-detail-note {
  padding: 0 48px;
  padding-top: 25px;
  margin-top: 40px;
  border-top: 1px solid #e0e6ed;
}
.invoice-detail-note .invoice-note {
  margin-bottom: 0;
}
.invoice-detail-note .invoice-note label {
  font-size: 14px;
  color: #3b3f5c;
  min-width: 75px;
  align-self: center;
  margin-bottom: 0;
}
.invoice-detail-note textarea {
  resize: none;
}

/*
======================
    Invoice Actions
======================
*/
.invoice-actions {
  padding: 0;
  padding-top: 32px;
  padding-bottom: 32px;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e0e6ed;
}
.invoice-actions label {
  font-size: 13px;
  font-weight: 600;
  color: #0e1726;
}
.invoice-actions .invoice-action-currency label {
  padding: 0 25px 10px 25px;
  padding-bottom: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e6ed;
  width: 100%;
  font-size: 16px;
  color: #0e1726;
  font-weight: 500;
}
.invoice-actions .invoice-action-currency .invoice-select {
  margin: 0 25px 0 25px;
}
.invoice-actions .invoice-action-currency a.dropdown-toggle {
  padding: 9px 38px 9px 45px;
  width: 100%;
}
.invoice-actions .invoice-action-currency a.dropdown-toggle span {
  vertical-align: middle;
}
.invoice-actions .invoice-action-currency .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu {
  width: 100%;
  padding: 6px 15px;
}
.invoice-actions .invoice-action-currency .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu .dropdown-item {
  padding: 10px 3px;
  border-radius: 0;
  font-size: 16px;
  line-height: 1.45;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
.invoice-actions .invoice-action-currency .dropdown:not(.custom-dropdown-icon):not(.custom-dropdown) .dropdown-menu img {
  vertical-align: sub;
}
.invoice-actions .invoice-action-tax {
  padding-top: 20px;
  margin-top: 20px;
}
.invoice-actions .invoice-action-tax h5 {
  padding: 0 25px 10px 25px;
  width: 100%;
  padding-bottom: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e6ed;
  width: 100%;
  font-size: 16px;
  color: #0e1726;
  font-weight: 500;
}
.invoice-actions .invoice-action-tax .invoice-action-tax-fields {
  margin: 0 25px 0 25px;
}
.invoice-actions .invoice-action-tax .input-rate {
  position: relative;
  padding: 9px 15px 10px 15px;
  border-radius: 8px;
  transform: none;
  font-size: 13px;
  line-height: 17px;
  background-color: #fff;
  letter-spacing: normal;
  text-align: inherit;
  color: #506690;
  box-shadow: none;
  max-height: 35px;
  display: inline-block;
}
.invoice-actions .invoice-action-discount {
  padding-top: 20px;
  margin-top: 20px;
}
.invoice-actions .invoice-action-discount .invoice-action-discount-fields {
  margin: 0 25px 0 25px;
}
.invoice-actions .invoice-action-discount h5 {
  width: 100%;
  padding: 0 25px 10px 25px;
  padding-bottom: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e6ed;
  width: 100%;
  font-size: 16px;
  color: #0e1726;
  font-weight: 500;
}
.invoice-actions .invoice-action-discount .input-rate {
  position: relative;
  padding: 9px 15px 10px 15px;
  border-radius: 8px;
  transform: none;
  font-size: 13px;
  line-height: 17px;
  background-color: #fff;
  letter-spacing: normal;
  text-align: inherit;
  color: #506690;
  box-shadow: none;
  max-height: 35px;
  display: inline-block;
}

/* Invoice Actions -> action-currency */
/* Invoice Actions -> action-tax */
/* Invoice Actions -> action-discount */
/*
===============================
    Invoice Actions Button
===============================
*/
.invoice-actions-btn {
  padding: 25px;
  padding-top: 32px;
  padding-bottom: 32px;
  margin-top: 25px;
  background-color: #fff;
  border: 1px solid #e0e6ed;
  border-radius: 8px;
}
.invoice-actions-btn label {
  font-size: 14px;
  font-weight: 600;
  color: #888ea8;
}
.invoice-actions-btn .invoice-action-btn a {
  -webkit-transform: none;
  transform: none;
}
.invoice-actions-btn .invoice-action-btn a.btn-send, .invoice-actions-btn .invoice-action-btn a.btn-preview {
  width: 100%;
  margin-bottom: 20px;
}
.invoice-actions-btn .invoice-action-btn a.btn-download {
  width: 100%;
  float: right;
}

/* Invoice Actions -> action-btn */
@media (max-width: 1199px) {
  .invoice-detail-body {
    margin-bottom: 50px;
  }
  .invoice-content .invoice-address-client {
    margin-top: 30px;
  }
  .invoice-actions-btn .invoice-action-btn a.btn-send, .invoice-actions-btn .invoice-action-btn a.btn-preview {
    margin-bottom: 0;
  }
}
@media (max-width: 767px) {
  .invoice-detail-total {
    padding: 0 25px;
  }
  .invoice-detail-note {
    padding: 0 25px;
    padding-top: 25px;
  }
  .invoice-detail-items {
    padding: 0 25px;
    background: transparent;
  }
  .invoice-detail-terms {
    padding-left: 25px;
    padding-right: 25px;
  }
  .invoice-content .invoice-detail-header {
    padding: 0 25px;
  }
  .invoice-content .invoice-detail-title {
    display: block;
    max-width: 320px;
    margin: 0 auto;
    margin-bottom: 40px;
  }
  .invoice-content .invoice-logo {
    margin-bottom: 15px;
  }
  .invoice-content .invoice-logo .dropify-wrapper {
    width: auto;
  }
  .totals-row {
    margin-left: auto;
    margin-right: auto;
    margin-top: 30px;
  }
  .invoice-detail-items thead {
    display: none;
  }
  .invoice-detail-items tbody td {
    display: block;
  }
  .invoice-detail-items tbody td.description {
    width: 100%;
    padding: 10px 4px;
    border: none;
  }
  .invoice-detail-items tbody td.rate, .invoice-detail-items tbody td.qty {
    display: inline-block;
    padding: 0 4px;
    border: none;
  }
  .invoice-detail-items tbody td.amount {
    display: inline-block;
    width: auto;
    border: none;
  }
  .invoice-detail-items tbody td.tax {
    width: auto;
    display: inline-block;
    padding: 12px 7px;
    border: none;
  }
  .invoice-detail-items tbody td.tax .new-chk-content {
    display: inline-block;
  }
  .invoice-detail-items tbody td.delete-item-row {
    padding: 0;
    border: none;
  }
  .invoice-detail-items tbody td.delete-item-row ul {
    position: absolute;
    left: 3px;
    top: 7px;
  }
  .invoice-detail-items tbody td.delete-item-row .delete-item {
    position: absolute;
    left: 6px;
    top: 1px;
  }
  .invoice-detail-items tbody tr {
    display: block;
    padding: 25px 0;
    border-radius: 8px;
    position: relative;
    border: none;
  }
  .invoice-detail-items tbody tr:not(:last-child) {
    margin-bottom: 16px;
  }
  .invoice-actions-btn .invoice-action-btn a.btn-send, .invoice-actions-btn .invoice-action-btn a.btn-preview {
    margin-bottom: 20px;
  }
}
@media (max-width: 575px) {
  .invoice-actions-btn .invoice-action-btn {
    width: 100%;
  }
  .selectable-dropdown a.dropdown-toggle {
    padding: 9px 20px 10px 15px;
  }
  .selectable-dropdown a.dropdown-toggle svg {
    top: 11px;
    right: 4px;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
