<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_reference',
        'room_id',
        'user_id',
        'guest_first_name',
        'guest_last_name',
        'guest_email',
        'guest_phone',
        'guest_gender',
        'check_in_date',
        'check_out_date',
        'duration_months',
        'total_amount',
        'deposit_amount',
        'monthly_rent',
        'status',
        'payment_status',
        'special_requests',
        'notes',
        'confirmed_at',
        'cancelled_at',
        'cancellation_reason'
    ];

    protected $casts = [
        'check_in_date' => 'date',
        'check_out_date' => 'date',
        'total_amount' => 'decimal:2',
        'deposit_amount' => 'decimal:2',
        'monthly_rent' => 'decimal:2',
        'confirmed_at' => 'datetime',
        'cancelled_at' => 'datetime'
    ];

    /**
     * Generate a unique booking reference
     */
    public static function generateReference(): string
    {
        do {
            $reference = 'BK' . strtoupper(Str::random(8));
        } while (self::where('booking_reference', $reference)->exists());

        return $reference;
    }

    /**
     * Boot method to auto-generate booking reference
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (empty($booking->booking_reference)) {
                $booking->booking_reference = self::generateReference();
            }
        });
    }

    /**
     * Relationship with Room
     */
    public function room()
    {
        return $this->belongsTo(Rooms::class);
    }

    /**
     * Relationship with User
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the customer name (user or guest)
     */
    public function getCustomerNameAttribute()
    {
        if ($this->user) {
            return $this->user->first_name . ' ' . $this->user->last_name;
        }
        
        return $this->guest_first_name . ' ' . $this->guest_last_name;
    }

    /**
     * Get the customer email (user or guest)
     */
    public function getCustomerEmailAttribute()
    {
        if ($this->user) {
            return $this->user->email;
        }
        
        return $this->guest_email;
    }

    /**
     * Get the customer phone (user or guest)
     */
    public function getCustomerPhoneAttribute()
    {
        if ($this->user) {
            return $this->user->phone;
        }
        
        return $this->guest_phone;
    }

    /**
     * Check if booking can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']) && 
               $this->check_in_date->isFuture();
    }

    /**
     * Check if booking can be confirmed
     */
    public function canBeConfirmed(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Confirm the booking
     */
    public function confirm(): bool
    {
        if (!$this->canBeConfirmed()) {
            return false;
        }

        $this->update([
            'status' => 'confirmed',
            'confirmed_at' => now()
        ]);

        // Update room availability
        $this->room->update(['IsAvailable' => 1]);

        return true;
    }

    /**
     * Cancel the booking
     */
    public function cancel(string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason
        ]);

        // Make room available again if it was confirmed
        if ($this->status === 'confirmed') {
            $this->room->update(['IsAvailable' => 0]);
        }

        return true;
    }

    /**
     * Calculate total amount based on duration and monthly rent
     */
    public function calculateTotalAmount(): float
    {
        return $this->duration_months * $this->monthly_rent + $this->deposit_amount;
    }

    /**
     * Scope for active bookings
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'confirmed']);
    }

    /**
     * Scope for confirmed bookings
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope for pending bookings
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for current bookings (check-in date has passed but not check-out)
     */
    public function scopeCurrent($query)
    {
        return $query->where('check_in_date', '<=', now())
                    ->where('check_out_date', '>=', now())
                    ->where('status', 'confirmed');
    }

    /**
     * Scope for upcoming bookings
     */
    public function scopeUpcoming($query)
    {
        return $query->where('check_in_date', '>', now())
                    ->where('status', 'confirmed');
    }
}
