<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Rooms;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class BookingApiController extends Controller
{
    /**
     * Display a listing of bookings
     */
    public function index(Request $request)
    {
        try {
            $query = Booking::with(['room', 'user']);

            // Filter by user if authenticated
            if ($request->user()) {
                $query->where('user_id', $request->user()->id);
            }

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('payment_status')) {
                $query->where('payment_status', $request->payment_status);
            }

            if ($request->has('room_id')) {
                $query->where('room_id', $request->room_id);
            }

            if ($request->has('from_date')) {
                $query->where('check_in_date', '>=', $request->from_date);
            }

            if ($request->has('to_date')) {
                $query->where('check_out_date', '<=', $request->to_date);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            
            $allowedSortFields = ['created_at', 'check_in_date', 'check_out_date', 'total_amount', 'status'];
            if (in_array($sortBy, $allowedSortFields)) {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Pagination
            $perPage = min($request->get('per_page', 15), 100);
            $bookings = $query->paginate($perPage);

            // Transform the data
            $bookings->getCollection()->transform(function ($booking) {
                return $this->transformBooking($booking);
            });

            return response()->json([
                'success' => true,
                'data' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch bookings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created booking
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'room_id' => 'required|exists:rooms,id',
                'check_in_date' => 'required|date|after:today',
                'duration_months' => 'required|integer|min:1|max:12',
                'guest_first_name' => 'required_without:user_id|string|max:255',
                'guest_last_name' => 'required_without:user_id|string|max:255',
                'guest_email' => 'required_without:user_id|email|max:255',
                'guest_phone' => 'required_without:user_id|string|max:20',
                'guest_gender' => 'required_without:user_id|string|in:male,female,other',
                'special_requests' => 'nullable|string|max:1000',
                'deposit_amount' => 'nullable|numeric|min:0',
            ]);

            // Check if room exists and is available
            $room = Rooms::find($validatedData['room_id']);
            if (!$room) {
                return response()->json([
                    'success' => false,
                    'message' => 'Room not found'
                ], 404);
            }

            if ($room->IsAvailable == 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'Room is not available for booking'
                ], 422);
            }

            // Calculate dates and amounts
            $checkInDate = Carbon::parse($validatedData['check_in_date']);
            $checkOutDate = $checkInDate->copy()->addMonths($validatedData['duration_months']);
            $monthlyRent = (float) $room->Price;
            $depositAmount = $validatedData['deposit_amount'] ?? ($monthlyRent * 0.5); // Default 50% of monthly rent
            $totalAmount = ($monthlyRent * $validatedData['duration_months']) + $depositAmount;

            // Check for overlapping bookings
            $overlappingBooking = Booking::where('room_id', $room->id)
                ->where('status', '!=', 'cancelled')
                ->where(function ($query) use ($checkInDate, $checkOutDate) {
                    $query->whereBetween('check_in_date', [$checkInDate, $checkOutDate])
                          ->orWhereBetween('check_out_date', [$checkInDate, $checkOutDate])
                          ->orWhere(function ($q) use ($checkInDate, $checkOutDate) {
                              $q->where('check_in_date', '<=', $checkInDate)
                                ->where('check_out_date', '>=', $checkOutDate);
                          });
                })
                ->exists();

            if ($overlappingBooking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Room is already booked for the selected dates'
                ], 422);
            }

            // Create booking
            $bookingData = [
                'room_id' => $room->id,
                'user_id' => $request->user() ? $request->user()->id : null,
                'check_in_date' => $checkInDate,
                'check_out_date' => $checkOutDate,
                'duration_months' => $validatedData['duration_months'],
                'monthly_rent' => $monthlyRent,
                'deposit_amount' => $depositAmount,
                'total_amount' => $totalAmount,
                'special_requests' => $validatedData['special_requests'] ?? null,
                'status' => 'pending',
                'payment_status' => 'unpaid'
            ];

            // Add guest information if not authenticated user
            if (!$request->user()) {
                $bookingData = array_merge($bookingData, [
                    'guest_first_name' => $validatedData['guest_first_name'],
                    'guest_last_name' => $validatedData['guest_last_name'],
                    'guest_email' => $validatedData['guest_email'],
                    'guest_phone' => $validatedData['guest_phone'],
                    'guest_gender' => $validatedData['guest_gender'],
                ]);
            }

            $booking = Booking::create($bookingData);
            $booking->load(['room', 'user']);

            return response()->json([
                'success' => true,
                'message' => 'Booking created successfully',
                'data' => $this->transformBooking($booking, true)
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified booking
     */
    public function show($id)
    {
        try {
            $booking = Booking::with(['room', 'user'])->find($id);

            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $this->transformBooking($booking, true)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified booking
     */
    public function update(Request $request, $id)
    {
        try {
            $booking = Booking::find($id);

            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found'
                ], 404);
            }

            // Only allow updates for pending bookings
            if ($booking->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending bookings can be updated'
                ], 422);
            }

            $validatedData = $request->validate([
                'check_in_date' => 'date|after:today',
                'duration_months' => 'integer|min:1|max:12',
                'special_requests' => 'nullable|string|max:1000',
                'guest_first_name' => 'string|max:255',
                'guest_last_name' => 'string|max:255',
                'guest_email' => 'email|max:255',
                'guest_phone' => 'string|max:20',
                'guest_gender' => 'string|in:male,female,other',
            ]);

            // Recalculate if dates or duration changed
            if (isset($validatedData['check_in_date']) || isset($validatedData['duration_months'])) {
                $checkInDate = isset($validatedData['check_in_date']) 
                    ? Carbon::parse($validatedData['check_in_date']) 
                    : $booking->check_in_date;
                
                $durationMonths = $validatedData['duration_months'] ?? $booking->duration_months;
                $checkOutDate = $checkInDate->copy()->addMonths($durationMonths);
                
                $validatedData['check_out_date'] = $checkOutDate;
                $validatedData['total_amount'] = ($booking->monthly_rent * $durationMonths) + $booking->deposit_amount;
            }

            $booking->update($validatedData);
            $booking->load(['room', 'user']);

            return response()->json([
                'success' => true,
                'message' => 'Booking updated successfully',
                'data' => $this->transformBooking($booking, true)
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a booking
     */
    public function cancel(Request $request, $id)
    {
        try {
            $booking = Booking::find($id);

            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found'
                ], 404);
            }

            $request->validate([
                'reason' => 'nullable|string|max:500'
            ]);

            if (!$booking->canBeCancelled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking cannot be cancelled'
                ], 422);
            }

            $booking->cancel($request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Booking cancelled successfully',
                'data' => $this->transformBooking($booking->fresh(['room', 'user']), true)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirm a booking (admin only)
     */
    public function confirm($id)
    {
        try {
            $booking = Booking::find($id);

            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found'
                ], 404);
            }

            if (!$booking->canBeConfirmed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking cannot be confirmed'
                ], 422);
            }

            $booking->confirm();

            return response()->json([
                'success' => true,
                'message' => 'Booking confirmed successfully',
                'data' => $this->transformBooking($booking->fresh(['room', 'user']), true)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to confirm booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Transform booking data for API response
     */
    private function transformBooking($booking, $detailed = false)
    {
        $data = [
            'id' => $booking->id,
            'booking_reference' => $booking->booking_reference,
            'room' => [
                'id' => $booking->room->id,
                'room_number' => $booking->room->RoomNumber,
                'title' => $booking->room->Title,
                'address' => $booking->room->Address,
            ],
            'customer_name' => $booking->customer_name,
            'customer_email' => $booking->customer_email,
            'check_in_date' => $booking->check_in_date->format('Y-m-d'),
            'check_out_date' => $booking->check_out_date->format('Y-m-d'),
            'duration_months' => $booking->duration_months,
            'monthly_rent' => (float) $booking->monthly_rent,
            'deposit_amount' => (float) $booking->deposit_amount,
            'total_amount' => (float) $booking->total_amount,
            'status' => $booking->status,
            'payment_status' => $booking->payment_status,
            'created_at' => $booking->created_at,
        ];

        if ($detailed) {
            $data['customer_phone'] = $booking->customer_phone;
            $data['special_requests'] = $booking->special_requests;
            $data['notes'] = $booking->notes;
            $data['confirmed_at'] = $booking->confirmed_at;
            $data['cancelled_at'] = $booking->cancelled_at;
            $data['cancellation_reason'] = $booking->cancellation_reason;
            $data['updated_at'] = $booking->updated_at;
        }

        return $data;
    }
}
