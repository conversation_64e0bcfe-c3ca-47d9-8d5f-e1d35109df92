<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class ApiRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $apiKey = $request->get('api_key'); // Set by ApiKeyAuth middleware
        
        if (!$apiKey) {
            // Fallback to IP-based rate limiting for non-API key requests
            return $this->handleIpRateLimit($request, $next);
        }

        return $this->handleApiKeyRateLimit($request, $next, $apiKey);
    }

    /**
     * Handle rate limiting for API key requests
     */
    private function handleApiKeyRateLimit(Request $request, Closure $next, $apiKey): Response
    {
        $cacheKey = "api_rate_limit:{$apiKey->id}";
        $windowStart = now()->startOfHour();
        $windowKey = $cacheKey . ':' . $windowStart->timestamp;
        
        $currentRequests = Cache::get($windowKey, 0);
        $rateLimit = $apiKey->rate_limit;

        if ($currentRequests >= $rateLimit) {
            return response()->json([
                'error' => 'Rate limit exceeded',
                'message' => "You have exceeded the rate limit of {$rateLimit} requests per hour",
                'retry_after' => $windowStart->addHour()->diffInSeconds(now())
            ], 429);
        }

        // Increment the counter
        Cache::put($windowKey, $currentRequests + 1, 3600); // Cache for 1 hour

        $response = $next($request);

        // Add rate limit headers
        $response->headers->set('X-RateLimit-Limit', $rateLimit);
        $response->headers->set('X-RateLimit-Remaining', max(0, $rateLimit - $currentRequests - 1));
        $response->headers->set('X-RateLimit-Reset', $windowStart->addHour()->timestamp);

        return $response;
    }

    /**
     * Handle rate limiting for IP-based requests (fallback)
     */
    private function handleIpRateLimit(Request $request, Closure $next): Response
    {
        $ip = $request->ip();
        $cacheKey = "ip_rate_limit:{$ip}";
        $windowStart = now()->startOfHour();
        $windowKey = $cacheKey . ':' . $windowStart->timestamp;
        
        $currentRequests = Cache::get($windowKey, 0);
        $rateLimit = 100; // Default 100 requests per hour for IP-based limiting

        if ($currentRequests >= $rateLimit) {
            return response()->json([
                'error' => 'Rate limit exceeded',
                'message' => "You have exceeded the rate limit of {$rateLimit} requests per hour",
                'retry_after' => $windowStart->addHour()->diffInSeconds(now())
            ], 429);
        }

        // Increment the counter
        Cache::put($windowKey, $currentRequests + 1, 3600);

        $response = $next($request);

        // Add rate limit headers
        $response->headers->set('X-RateLimit-Limit', $rateLimit);
        $response->headers->set('X-RateLimit-Remaining', max(0, $rateLimit - $currentRequests - 1));
        $response->headers->set('X-RateLimit-Reset', $windowStart->addHour()->timestamp);

        return $response;
    }
}
