<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Rate extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'faculty',
        'rate',
        'photo',
        'description',
        
    ];

    public function getPhotoURLAttribute(){
        return asset('uploads/images\\').$this->photo;
    }
}
